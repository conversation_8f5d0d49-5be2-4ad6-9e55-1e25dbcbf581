package com.datascope.app.common.config;

import cn.hutool.core.util.StrUtil;
import com.datascope.app.entity.Datasource;
import com.datascope.app.util.JdbcUrlBuilder;
import com.yeepay.g3.utils.common.datasource.Config;
import com.yeepay.g3.utils.common.datasource.MonitorDataSource;
import com.yeepay.g3.utils.common.datasource.PooledDataSourceFactory;
import com.yeepay.g3.utils.common.datasource.properties.EnvVarProperties;
import com.yeepay.g3.utils.common.datasource.properties.FileProperties;
import com.yeepay.g3.utils.common.encrypt.AES;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.FactoryBean;

import javax.sql.DataSource;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 *
 * 动态数据源工厂类
 */
public class DynamicDataSourceFactoryBean implements FactoryBean<Object> {

    private static final Logger logger = LoggerFactory.getLogger(DynamicDataSourceFactoryBean.class);

    @Setter
    private String name;

    @Getter
    @Setter
    private Datasource dataSource;

    private static final Map<String, DataSource> DATA_SOURCES = new ConcurrentHashMap<>();
    /**
     * Pooled数据源工厂
     */
    @Setter
    PooledDataSourceFactory pooledDataSourceFactory = null;

    @Override
    public Object getObject() throws Exception {
        DataSource ds = DATA_SOURCES.get(name);
        if (ds == null) {
            synchronized (DynamicDataSourceFactoryBean.class) {
                ds = DATA_SOURCES.get(name);
                if (ds == null) {
                    ds = creatDataSource();
                    DATA_SOURCES.put(name, ds);
                }
            }
        } else {
            logger.info("load dynamic datasource : " + name);
        }
        return ds;
    }


    private DataSource creatDataSource() {
        logger.info("create datasource : " + name);
        try {
            Properties prop = loadDataSourceConfig();
            Config config = Config.instance(prop);
            config.setFileName(name);
            config.setDriverClass(JdbcUrlBuilder.getDriverClassName(dataSource.getType()));
            config.setJdbcUrl(JdbcUrlBuilder.buildJdbcUrl(dataSource));
            config.setUser(dataSource.getUsername());
            config.setPassword(AES.encryptToBase64(dataSource.getPassword(), Config.AES_KEY));
            config.setSchema(dataSource.getSchema());
            // SELECT 1 FROM SYSIBM.SYSDUMMY1
            if (StrUtil.isNotBlank(config.getValidationQuery())) {
                if (config.getJdbcUrl().contains("db2")) {
                    config.setValidationQuery("SELECT 1 FROM SYSIBM.SYSDUMMY1");
                }
                if (config.getJdbcUrl().contains("mysql")) {
                    config.setValidationQuery("SELECT 1");
                }
            }
            DataSource result = pooledDataSourceFactory.getDataSource(config);
            // 默认开启监控
            MonitorDataSource monitor = new MonitorDataSource();
            monitor.setDataSource(result);
            monitor.setName(name);
            monitor.setReleaseAlarmThreshold(config.getReleaseAlarmThreshold());
            monitor.setBlockingThreshold(config.getBlockingThreshold());
            result = monitor;
            return result;
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private Properties loadDataSourceConfig() throws IOException {
        Properties prop;
        if (isEnvVar()) {
            prop = new EnvVarProperties().load("DATA_SCOPE");
        } else {
            prop = new FileProperties().load("DATA_SCOPE");
        }
        return prop;
    }

    private boolean isEnvVar() {
        String value = System.getProperty("yeepay.datasource.config");
        return "env".equals(value);
    }

    @Override
    public Class getObjectType() {
        return DataSource.class;
    }

}
