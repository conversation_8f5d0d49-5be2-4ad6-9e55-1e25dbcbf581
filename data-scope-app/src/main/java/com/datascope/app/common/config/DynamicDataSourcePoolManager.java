package com.datascope.app.common.config;

import cn.hutool.core.util.StrUtil;
import com.datascope.app.entity.Datasource;
import com.yeepay.g3.utils.common.datasource.impl.DruidPooledDataSourceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 *
 * 动态数据源连接池管理
 */
@Slf4j
@Component
public class DynamicDataSourcePoolManager {

    /**
     * 缓存JdbcTemplate实例
     */
    private final Map<String, JdbcTemplate> jdbcTemplateCache = new ConcurrentHashMap<>();

    /**
     * 获取JdbcTemplate（复用实例）
     */
    public JdbcTemplate getJdbcTemplate(Datasource datasource, String schema) {
        String cacheKey = generateCacheKey(datasource.getId(), schema);

        return jdbcTemplateCache.computeIfAbsent(cacheKey, key -> {
            try {
                return createJdbcTemplate(datasource, schema);
            } catch (Exception e) {
                log.error("创建JdbcTemplate失败: datasourceId={}, schema={}", datasource.getId(), schema, e);
                throw new RuntimeException("创建JdbcTemplate失败", e);
            }
        });
    }

    /**
     * 创建JdbcTemplate（使用公司封装的Druid连接池）
     */
    private JdbcTemplate createJdbcTemplate(Datasource datasource, String schema) {

        // 使用公司封装的DataSourceFactoryBean创建数据源
        DataSource dataSource = createCompanyDruidDataSource(datasource, schema);

        // 创建JdbcTemplate
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
        jdbcTemplate.setQueryTimeout(30);
        jdbcTemplate.setFetchSize(1000);

        log.info("成功创建JdbcTemplate: datasourceId={}, schema={}", datasource.getId(), schema);
        return jdbcTemplate;
    }

    /**
     * 使用公司封装的DataSourceFactoryBean创建数据源
     */
    private DataSource createCompanyDruidDataSource(Datasource datasource, String schema) {
        try {
            // 使用公司封装的DataSourceFactoryBean
            final DynamicDataSourceFactoryBean factoryBean = new DynamicDataSourceFactoryBean();
            factoryBean.setName("QueryPool-" + datasource.getId() + "-" + schema);
            factoryBean.setPooledDataSourceFactory(new DruidPooledDataSourceFactory());
            factoryBean.setDataSource(datasource);

            // 获取数据源
            DataSource dataSource = (DataSource) factoryBean.getObject();

            log.info("成功创建公司动态Druid连接池: datasourceId={}, schema={}, poolName={}",
                datasource.getId(), schema, "QueryPool-" + datasource.getId() + "-" + schema);

            return dataSource;

        } catch (Exception e) {
            log.error("创建公司Druid连接池失败: datasourceId={}, schema={}", datasource.getId(), schema, e);
            throw new RuntimeException("创建公司Druid连接池失败", e);
        }
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(String datasourceId, String schema) {
        return datasourceId + "_" + (StrUtil.isEmpty(schema) ? "default" : schema);
    }
}
