package com.datascope.app.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 查询限流配置
 * 用于控制高并发查询接口的访问频率
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "datascope.query.rate-limit")
public class QueryRateLimitConfig {

    /**
     * 是否启用限流
     */
    private boolean enabled = true;

    /**
     * 每秒最大请求数
     */
    private int maxRequestsPerSecond = 2000;

    /**
     * 每个用户每秒最大请求数
     */
    private int maxRequestsPerUserPerSecond = 200;

    /**
     * 每个数据源每秒最大请求数
     */
    private int maxRequestsPerDataSourcePerSecond = 500;

    /**
     * 每个集成每秒最大请求数
     */
    private int maxRequestsPerIntegrationPerSecond = 300;

    /**
     * 限流窗口大小（秒）
     */
    private int windowSize = 1;

    /**
     * 限流策略：TOKEN_BUCKET, LEAKY_BUCKET, SLIDING_WINDOW
     */
    private String strategy = "SLIDING_WINDOW";

    /**
     * 限流失败时的降级策略：REJECT, QUEUE, CACHE
     */
    private String fallbackStrategy = "CACHE";

    /**
     * 缓存过期时间（秒）
     */
    private int cacheExpireSeconds = 600;

    /**
     * 队列最大长度
     */
    private int maxQueueSize = 1000;

    /**
     * 队列等待超时时间（毫秒）
     */
    private long queueTimeoutMs = 5000;
}
