package com.datascope.app.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 查询超时配置
 * 使用@Value注解直接从DATA_SCOPE.properties文件读取配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
public class QueryTimeoutConfig {

    /**
     * 查询超时时间（秒）
     */
    @Value("${queryTimeout:300}")
    private int queryTimeoutMs;

        /**
     * Socket超时时间（毫秒）
     */
    @Value("${socketTimeout:300}")
    private int socketTimeoutMs;

    /**
     * 连接超时时间（毫秒）
     */
    @Value("${connectTimeout:5000}")
    private int connectTimeoutMs;

    /**
     * 是否启用自动重连
     */
    @Value("${autoReconnect:true}")
    private boolean autoReconnect;

    /**
     * 是否使用SSL
     */
    @Value("${useSSL:false}")
    private boolean useSSL;

    /**
     * 是否允许公钥检索（MySQL 8.0兼容性）
     */
    @Value("${allowPublicKeyRetrieval:true}")
    private boolean allowPublicKeyRetrieval;

    /**
     * 获取查询超时时间（秒）
     */
    public int getTimeoutSeconds() {
        return queryTimeoutMs;
    }

    /**
     * 获取连接超时时间（毫秒）
     */
    public int getConnectionTimeout() {
        return connectTimeoutMs;
    }

    /**
     * 获取Socket超时时间（毫秒）
     */
    public int getSocketTimeout() {
        return socketTimeoutMs * 1000;
    }
}
