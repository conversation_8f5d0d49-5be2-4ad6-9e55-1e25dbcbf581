package com.datascope.app.common.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class ThreadPoolConfig {

    @Bean("resourceExecutor")
    public ThreadPoolExecutor resourceExecutor() {
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        int maxPoolSize = corePoolSize * 2;
        int queueCapacity = 1000;
        long keepAliveTime = 60L;

        return new ThreadPoolExecutor(
            corePoolSize,
            maxPoolSize,
            keepAliveTime,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(queueCapacity),
            new ThreadFactoryBuilder()
                .setNameFormat("get-resource-pool-%d")
                .build(),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    /**
     * 高并发查询执行线程池
     * 专门用于处理大量查询请求
     */
    @Bean("queryExecutor")
    public ThreadPoolExecutor queryExecutor() {
        int corePoolSize = Runtime.getRuntime().availableProcessors() * 2;
        int maxPoolSize = Runtime.getRuntime().availableProcessors() * 4;
        int queueCapacity = 5000; // 更大的队列容量
        long keepAliveTime = 120L;

        return new ThreadPoolExecutor(
            corePoolSize,
            maxPoolSize,
            keepAliveTime,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(queueCapacity),
            new ThreadFactoryBuilder()
                .setNameFormat("query-executor-%d")
                .setUncaughtExceptionHandler((t, e) ->
                    log.error("查询线程异常: {}", t.getName() + " - " + e.getMessage()))
                .build(),
            new ThreadPoolExecutor.CallerRunsPolicy() // 队列满时，调用者线程执行
        );
    }

    /**
     * Dubbo查询专用线程池
     * 专门用于处理Dubbo接口的查询请求
     */
    @Bean("dubboQueryExecutor")
    public ThreadPoolExecutor dubboQueryExecutor() {
        int corePoolSize = Runtime.getRuntime().availableProcessors() * 2;
        int maxPoolSize = Runtime.getRuntime().availableProcessors() * 4;
        int queueCapacity = 3000;
        long keepAliveTime = 180L;

        return new ThreadPoolExecutor(
            corePoolSize,
            maxPoolSize,
            keepAliveTime,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(queueCapacity),
            new ThreadFactoryBuilder()
                .setNameFormat("dubbo-query-executor-%d")
                .setUncaughtExceptionHandler((t, e) ->
                    log.error("Dubbo查询线程异常: {}", t.getName() + " - " + e.getMessage()))
                .build(),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    /**
     * 数据库连接池监控线程池
     */
    @Bean("dbMonitorExecutor")
    public ThreadPoolExecutor dbMonitorExecutor() {
        return new ThreadPoolExecutor(
            2,
            4,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100),
            new ThreadFactoryBuilder()
                .setNameFormat("db-monitor-%d")
                .setDaemon(true)
                .build(),
            new ThreadPoolExecutor.DiscardPolicy()
        );
    }
}
