package com.datascope.app.common.enums;

import lombok.Getter;

/**
 * 查询类型
 *
 * <AUTHOR>
 */
@Getter
public enum QueryTypeEnum {

    /**
     * sql
     */
    SQL("SQL", "SQL"),

    /**
     * database
     */
   LANGUAGE("NATURAL-LANGUAGE", "NATURAL-LANGUAGE")
    ;


    private final String code;
    private final String desc;

    QueryTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
