package com.datascope.app.common.response;

import lombok.Getter;

/**
 *<AUTHOR>
 *
 * Business exception
 */
@Getter
public class DubboCheckException extends RuntimeException {

    /**
     * Error code
     * -- GETTER --
     *  Get error code
     *
     * @return Error code

     */
    private final String code;

    /**
     * Create business exception with code and message
     *
     * @param code Error code
     * @param message Error message
     */
    public DubboCheckException(String code, String message) {
        super(message);
        this.code = code;
    }

    public DubboCheckException(String message) {
        super(message);
        this.code = "-1";
    }

}
