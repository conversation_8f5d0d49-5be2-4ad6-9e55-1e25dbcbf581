package com.datascope.app.config;

import com.datascope.app.common.response.Response;
import com.datascope.app.config.anno.QueryRateLimit;
import com.datascope.app.service.QueryCacheService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 基于令牌桶算法的查询限流切面
 * 使用Redis实现多实例共享的限流逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class QueryRateLimitAspect {

    @Autowired
    private QueryCacheService queryCacheService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private RateLimitConfig rateLimitConfig;

    // Redis key前缀
    private static final String GLOBAL_LIMIT_PREFIX = "rate_limit:global";
    private static final String USER_LIMIT_PREFIX = "rate_limit:user";
    private static final String DATA_SOURCE_LIMIT_PREFIX = "rate_limit:datasource";
    private static final String INTEGRATION_LIMIT_PREFIX = "rate_limit:integration";

    private static final String TOKEN_BUCKET_SCRIPT =
        "local key = KEYS[1] " +
        "local now = tonumber(ARGV[1]) " +
        "local capacity = tonumber(ARGV[2]) " +
        "local rate = tonumber(ARGV[3]) " +
        "local requested = tonumber(ARGV[4]) " +
        " " +
        "if not now or not capacity or not rate or not requested then " +
        "    return 0 " +
        "end " +
        " " +
        "local lastTime = redis.call('HGET', key, 'last_time') " +
        "local currentTokens = redis.call('HGET', key, 'tokens') " +
        " " +
        "if not lastTime or lastTime == false then " +
        "    currentTokens = capacity " +
        "    lastTime = now " +
        "    redis.call('HSET', key, 'last_time', now) " +
        "    redis.call('HSET', key, 'tokens', currentTokens) " +
        "    redis.call('EXPIRE', key, 60) " +
        "else " +
        "    lastTime = tonumber(lastTime) " +
        "    if not lastTime then " +
        "        currentTokens = capacity " +
        "        lastTime = now " +
        "        redis.call('HSET', key, 'last_time', now) " +
        "        redis.call('HSET', key, 'tokens', currentTokens) " +
        "        redis.call('EXPIRE', key, 60) " +
        "    else " +
        "        currentTokens = tonumber(currentTokens) " +
        "        if not currentTokens then " +
        "            currentTokens = 0 " +
        "        end " +
        "        " +
        "        local timePassed = now - lastTime " +
        "        if timePassed > 0 then " +
        "            local newTokens = math.floor(timePassed * rate) " +
        "            currentTokens = math.min(capacity, currentTokens + newTokens) " +
        "        end " +
        "    end " +
        "end " +
        " " +
        "if currentTokens >= requested then " +
        "    currentTokens = currentTokens - requested " +
        "    redis.call('HSET', key, 'last_time', now) " +
        "    redis.call('HSET', key, 'tokens', currentTokens) " +
        "    redis.call('EXPIRE', key, 60) " +
        "    return 1 " +
        "else " +
        "    return 0 " +
        "end";


    private final DefaultRedisScript<Long> tokenBucketScript;

    public QueryRateLimitAspect() {
        this.tokenBucketScript = new DefaultRedisScript<>();
        this.tokenBucketScript.setScriptText(TOKEN_BUCKET_SCRIPT);
        this.tokenBucketScript.setResultType(Long.class);
    }

    @Around("@annotation(queryRateLimit)")
    public Object around(ProceedingJoinPoint joinPoint, QueryRateLimit queryRateLimit) throws Throwable {
        try {
            // 检查全局限流
            if (!checkTokenBucketGlobalRateLimit(queryRateLimit.maxRequestsPerSecond())) {
                log.warn("全局限流触发");
                return createRateLimitResponse(queryRateLimit.errorMessage());
            }

//            // 检查用户级限流
//            if (queryRateLimit.enableUserLimit()) {
//                String userId = getCurrentUserId();
//                if (userId != null && !checkTokenBucketUserRateLimit(userId, queryRateLimit.maxRequestsPerUserPerSecond())) {
//                    log.warn("用户级限流触发，用户: {}", userId);
//                    return createRateLimitResponse(queryRateLimit.errorMessage());
//                }
//            }
//
//            // 检查数据源级限流
//            if (queryRateLimit.enableDataSourceLimit()) {
//                String dataSourceId = getDataSourceId(joinPoint);
//                if (dataSourceId != null && !checkTokenBucketDataSourceRateLimit(dataSourceId, queryRateLimit.maxRequestsPerDataSourcePerSecond())) {
//                    log.warn("数据源级限流触发，数据源: {}", dataSourceId);
//                    return createRateLimitResponse(queryRateLimit.errorMessage());
//                }
//            }
//
//            // 检查集成级限流
//            if (queryRateLimit.enableIntegrationLimit()) {
//                String integrationId = getIntegrationId(joinPoint);
//                if (integrationId != null && !checkTokenBucketIntegrationRateLimit(integrationId, queryRateLimit.maxRequestsPerIntegrationPerSecond())) {
//                    log.warn("集成级限流触发，集成: {}", integrationId);
//                    return createRateLimitResponse(queryRateLimit.errorMessage());
//                }
//            }

            // 执行原方法
            Object result = joinPoint.proceed();

            return result;

        } catch (Exception e) {
            log.error("查询限流切面执行异常", e);

            // 限流降级：根据配置的降级策略处理
            if ("CACHE".equals(queryRateLimit.fallbackStrategy())) {
                log.info("启用缓存降级策略");
                return handleCacheFallback(joinPoint, queryRateLimit);
            }

            throw e;
        }
    }

    /**
     * 检查令牌桶全局限流
     */
    private boolean checkTokenBucketGlobalRateLimit(int maxRequestsPerSecond) {
        // 使用配置的令牌桶参数，如果没有配置则使用注解参数
        int capacity = rateLimitConfig.getTokenBucket().getCapacity();
        int rate = rateLimitConfig.getTokenBucket().getRate();

        log.debug("全局限流检查 - 配置容量: {}, 配置速率: {}, 注解限制: {}",
                 capacity, rate, maxRequestsPerSecond);

        return checkTokenBucket(GLOBAL_LIMIT_PREFIX, capacity, rate);
    }

    /**
     * 检查令牌桶用户级限流
     */
    private boolean checkTokenBucketUserRateLimit(String userId, int maxRequestsPerUserPerSecond) {
        String key = USER_LIMIT_PREFIX + ":" + userId;
        int capacity = rateLimitConfig.getTokenBucket().getCapacity();
        int rate = rateLimitConfig.getTokenBucket().getRate();

        log.debug("用户限流检查 - 用户: {}, 配置容量: {}, 配置速率: {}, 注解限制: {}",
                 userId, capacity, rate, maxRequestsPerUserPerSecond);

        return checkTokenBucket(key, capacity, rate);
    }

    /**
     * 检查令牌桶数据源级限流
     */
    private boolean checkTokenBucketDataSourceRateLimit(String dataSourceId, int maxRequestsPerDataSourcePerSecond) {
        String key = DATA_SOURCE_LIMIT_PREFIX + ":" + dataSourceId;
        int capacity = rateLimitConfig.getTokenBucket().getCapacity();
        int rate = rateLimitConfig.getTokenBucket().getRate();

        log.debug("数据源限流检查 - 数据源: {}, 配置容量: {}, 配置速率: {}, 注解限制: {}",
                 dataSourceId, capacity, rate, maxRequestsPerDataSourcePerSecond);

        return checkTokenBucket(key, capacity, rate);
    }

    /**
     * 检查令牌桶集成级限流
     */
    private boolean checkTokenBucketIntegrationRateLimit(String integrationId, int maxRequestsPerIntegrationPerSecond) {
        String key = INTEGRATION_LIMIT_PREFIX + ":" + integrationId;
        int capacity = rateLimitConfig.getTokenBucket().getCapacity();
        int rate = rateLimitConfig.getTokenBucket().getRate();

        log.debug("集成限流检查 - 集成: {}, 配置容量: {}, 配置速率: {}, 注解限制: {}",
                 integrationId, capacity, rate, maxRequestsPerIntegrationPerSecond);

        return checkTokenBucket(key, capacity, rate);
    }

    /**
     * 令牌桶算法核心逻辑
     *
     * @param key Redis键
     * @param capacity 桶容量
     * @param rate 令牌生成速率（每秒）
     * @return 是否允许请求
     */
    private boolean checkTokenBucket(String key, int capacity, int rate) {
        try {

            double now = System.currentTimeMillis() / 1000.0;

            // 直接传递数值对象，不要转字符串
            Object[] args = new Object[] {
                now,           // ARGV[1] - 当前时间（double）
                (double)capacity, // ARGV[2] - 桶容量（double）
                rate,          // ARGV[3] - 令牌生成速率（double）
                1.0           // ARGV[4] - 请求1个令牌（double）
            };
            // 添加详细的调试日志
            log.info("令牌桶限流检查 - key: {}, now: {}, capacity: {}, rate: {}, requested: 1",
                     key, now, capacity, rate);
            log.info("Redis脚本参数 - keys: {}, args: {}", key, args);

            Long result = redisTemplate.execute(
                tokenBucketScript,
                Collections.singletonList(key), // KEYS
                args                            // ARGV
            );

            // 添加结果调试日志
            log.info("Redis脚本执行结果: {}", result);

            return result != null && result == 1;

        } catch (Exception e) {
            log.error("检查令牌桶限流失败，key: {}, 降级为允许", key, e);
            return true;
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                return request.getHeader("X-User-Id"); // 从请求头获取用户ID
            }
        } catch (Exception e) {
            log.warn("获取用户ID失败", e);
        }
        return null;
    }

    /**
     * 获取数据源ID
     */
    private String getDataSourceId(ProceedingJoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof com.datascope.facade.dto.QueryParamDTO) {
                com.datascope.facade.dto.QueryParamDTO paramDTO = (com.datascope.facade.dto.QueryParamDTO) args[0];
                // 通过集成ID获取数据源ID的逻辑
                return null; // 需要根据实际业务逻辑实现
            }
        } catch (Exception e) {
            log.warn("获取数据源ID失败", e);
        }
        return null;
    }

    /**
     * 获取集成ID
     */
    private String getIntegrationId(ProceedingJoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof com.datascope.facade.dto.QueryParamDTO) {
                com.datascope.facade.dto.QueryParamDTO paramDTO = (com.datascope.facade.dto.QueryParamDTO) args[0];
                return paramDTO.getId(); // 集成ID
            }
        } catch (Exception e) {
            log.warn("获取集成ID失败", e);
        }
        return null;
    }

    /**
     * 创建限流响应
、     */
    private Response<?> createRateLimitResponse(String message) {
        return Response.error(429, message);
    }

    /**
     * 处理缓存降级
     */
    private Object handleCacheFallback(ProceedingJoinPoint joinPoint, QueryRateLimit queryRateLimit) {
        try {
            // 获取方法参数
            Object[] args = joinPoint.getArgs();
            if (args.length > 0 && args[0] instanceof com.datascope.facade.dto.QueryParamDTO) {
                com.datascope.facade.dto.QueryParamDTO paramDTO = (com.datascope.facade.dto.QueryParamDTO) args[0];
                String integrationId = paramDTO.getId();
                Map<String, Object> parameters = paramDTO.getParams();

                // 生成缓存键
                String cacheKey = "integration_query:" + integrationId;
                if (parameters != null && !parameters.isEmpty()) {
                    cacheKey += ":" + parameters.hashCode();
                }

                // 尝试从缓存获取数据
                if (queryCacheService != null) {
                    Object cachedResult = queryCacheService.getCachedResult(cacheKey);
                    if (cachedResult != null) {
                        log.info("限流降级成功：返回缓存数据, integrationId={}", integrationId);
                        return createFallbackResponse(cachedResult);
                    }
                }
            }

            // 缓存中没有数据，返回限流响应
            log.warn("限流降级失败：无缓存数据");
            return createRateLimitResponse("系统繁忙，请稍后重试");

        } catch (Exception ex) {
            log.error("处理缓存降级异常", ex);
            return createRateLimitResponse("系统繁忙，请稍后重试");
        }
    }

    /**
     * 创建降级响应
     */
    private Object createFallbackResponse(Object cachedData) {
        // 根据实际业务需求构造降级响应
        return cachedData;
    }
}
