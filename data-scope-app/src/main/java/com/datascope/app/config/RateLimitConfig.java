package com.datascope.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PostConstruct;

/**
 * 令牌桶限流配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "yeepay.rate-limit")
@EnableConfigurationProperties
public class RateLimitConfig {

    /**
     * 降级策略
     */
    private String fallbackStrategy = "REJECT"; // REJECT, CACHE, QUEUE

    /**
     * Redis配置
     */
    private Redis redis = new Redis();

    /**
     * 令牌桶配置
     */
    private TokenBucket tokenBucket = new TokenBucket();

    @Data
    public static class Redis {
        /**
         * Redis连接超时时间（毫秒）
         */
        private int connectionTimeout = 3000;

        /**
         * Redis操作超时时间（毫秒）
         */
        private int operationTimeout = 1000;

        /**
         * 限流key过期时间（秒）
         */
        private int keyExpireSeconds = 60;
    }

    @Data
    public static class TokenBucket {
        /**
         * 令牌桶容量
         */
        private int capacity; // 默认值

        /**
         * 令牌生成速率（每秒）
         */
        private int rate; // 默认值

        /**
         * 突发流量容忍度（倍数）
         */
        private double burstTolerance = 2.0;
    }

    /**
     * 降级策略枚举
     */
    public enum FallbackStrategy {
        REJECT("直接拒绝"),
        CACHE("返回缓存数据"),
        QUEUE("排队等待");

        private final String description;

        FallbackStrategy(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 初始化后打印配置信息
     */
    @PostConstruct
    public void printConfig() {
        System.out.println("=== RateLimitConfig 配置信息 ===");
        System.out.println("fallbackStrategy: " + fallbackStrategy);
        System.out.println("redis.connectionTimeout: " + redis.getConnectionTimeout());
        System.out.println("redis.operationTimeout: " + redis.getOperationTimeout());
        System.out.println("redis.keyExpireSeconds: " + redis.getKeyExpireSeconds());
        System.out.println("tokenBucket.capacity: " + tokenBucket.getCapacity());
        System.out.println("tokenBucket.rate: " + tokenBucket.getRate());
        System.out.println("tokenBucket.burstTolerance: " + tokenBucket.getBurstTolerance());
        System.out.println("================================");
    }
}
