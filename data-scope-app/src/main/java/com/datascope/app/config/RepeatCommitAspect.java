package com.datascope.app.config;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.datascope.app.config.anno.RepeatCommit;
import com.datascope.app.constants.CommonConst;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Aspect
public class RepeatCommitAspect {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Around(value = "@annotation(com.datascope.app.config.anno.RepeatCommit) || @within(com.datascope.app.config.anno.RepeatCommit)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String uri = request.getRequestURI();
        log.info("request uri: {}", uri);
        String remoteAddr = request.getRemoteAddr();
        log.info("request remoteAddr: {}", remoteAddr);
        Method method = getMethod(joinPoint);
        String declaringName = method.getDeclaringClass().getName();
        String methodName = method.getName();
        String key = remoteAddr + "_" + uri + "_" + declaringName + "_" + methodName;
        String repeatCommitKey = CommonConst.getRepeatCommitKey(key);
        RepeatCommit annotation = method.getAnnotation(RepeatCommit.class);
        if (!method.isAnnotationPresent(RepeatCommit.class)) {
            joinPoint.proceed();
        }
        long timeout = annotation.timeout() < 0 ? 1000 : annotation.timeout();
        String value = redisTemplate.opsForValue().get(repeatCommitKey);
        if (StrUtil.isNotBlank(value)) {
            throw new RuntimeException("请勿重复操作!");
        }
//        Object object = null;
//        try {
        Object object = joinPoint.proceed();
//        } catch (Throwable throwable) {
//            log.info("proceed aspect error: ", throwable);
//        }
        redisTemplate.opsForValue().set(repeatCommitKey, IdUtil.objectId(), timeout, TimeUnit.MILLISECONDS);
        return object;
    }

    private Method getMethod(ProceedingJoinPoint joinPoint) {
        return ((MethodSignature) joinPoint.getSignature()).getMethod();
    }
}
