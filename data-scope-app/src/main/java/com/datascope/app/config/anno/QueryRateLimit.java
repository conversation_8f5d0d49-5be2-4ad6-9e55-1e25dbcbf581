package com.datascope.app.config.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 查询限流注解
 * 用于标记需要限流的查询接口
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface QueryRateLimit {

    /**
     * 每秒最大请求数
     */
    int maxRequestsPerSecond() default 2000;

    /**
     * 每个用户每秒最大请求数
     */
    int maxRequestsPerUserPerSecond() default 200;

    /**
     * 每个数据源每秒最大请求数
     */
    int maxRequestsPerDataSourcePerSecond() default 500;

    /**
     * 每个集成每秒最大请求数
     */
    int maxRequestsPerIntegrationPerSecond() default 300;

    /**
     * 限流窗口大小（秒）
     */
    int windowSize() default 1;

    /**
     * 限流策略
     */
    String strategy() default "SLIDING_WINDOW";

    /**
     * 降级策略
     */
    String fallbackStrategy() default "CACHE";

    /**
     * 缓存过期时间（秒）
     */
    int cacheExpireSeconds() default 600;

    /**
     * 是否启用用户级限流
     */
    boolean enableUserLimit() default true;

    /**
     * 是否启用数据源级限流
     */
    boolean enableDataSourceLimit() default true;

    /**
     * 是否启用集成级限流
     */
    boolean enableIntegrationLimit() default true;

    /**
     * 限流失败时的错误消息
     */
    String errorMessage() default "请求过于频繁，请稍后重试";
}
