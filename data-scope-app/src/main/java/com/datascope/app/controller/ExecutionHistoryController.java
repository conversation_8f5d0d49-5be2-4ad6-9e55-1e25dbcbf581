package com.datascope.app.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datascope.app.common.response.Response;
import com.datascope.app.dto.query.ExecutionHistoryDTO;
import com.datascope.app.dto.query.ExecutionHistoryQueryParam;
import com.datascope.app.service.ExecutionHistoryService;
import com.datascope.app.vo.response.PageResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 执行历史管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/execution-history")
@Tag(name = "执行历史管理模块", description = "执行历史相关接口")
public class ExecutionHistoryController {

    private final ExecutionHistoryService executionHistoryService;

    /**
     * 获取执行历史列表
     */
    @GetMapping
    @Operation(summary = "获取执行历史列表")
    public Response<PageResultVo<ExecutionHistoryDTO>> getExecutionHistory(
        @RequestParam(defaultValue = "1") int page,
        @RequestParam(defaultValue = "20") int size,
        @RequestParam(required = false) String dataSourceName,
        @RequestParam(required = false) String schemaName,
        @RequestParam(required = false) String executedBy,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date executedAtStart,
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date executedAtEnd,
        @RequestParam(required = false) String status,
        @RequestParam(required = false) String queryId,
        @RequestParam(required = false) String sortBy,
        @RequestParam(defaultValue = "desc") String sortDir) {

        log.info("获取执行历史列表, page={}, size={}, dataSourceName={}, schemaName={}, executedBy={}, executedAtStart={}, executedAtEnd={}, status={}, queryId={}, sortBy={}, sortDir={}",
            page, size, dataSourceName, schemaName, executedBy, executedAtStart, executedAtEnd, status, queryId, sortBy, sortDir);

        ExecutionHistoryQueryParam queryParam = ExecutionHistoryQueryParam.builder()
            .page(page)
            .size(size)
            .dataSourceName(dataSourceName)
            .schemaName(schemaName)
            .executedBy(executedBy)
            .executedAtStart(executedAtStart)
            .executedAtEnd(executedAtEnd)
            .status(status)
            .queryId(queryId)
            .sortBy(sortBy)
            .sortDir(sortDir)
            .build();

        Page<ExecutionHistoryDTO> pageResult = executionHistoryService.getExecutionHistory(queryParam);

        PageResultVo<ExecutionHistoryDTO> build = PageResultVo.<ExecutionHistoryDTO>builder()
            .items(pageResult.getRecords())
            .total(pageResult.getTotal())
            .page(page).size(size)
            .totalPages(pageResult.getPages())
            .build();

        return Response.ok(build);
    }
}
