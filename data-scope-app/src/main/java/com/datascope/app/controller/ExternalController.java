package com.datascope.app.controller;

import cn.hutool.json.JSONUtil;
import com.datascope.app.common.response.Response;
import com.datascope.app.config.anno.RepeatCommit;
import com.datascope.app.dto.integration.DataSourceQueryDTO;
import com.datascope.app.dto.integration.IntegrationCallBackDTO;
import com.datascope.app.dto.integration.UpdateIntegrationRequest;
import com.datascope.app.dto.query.SaveQueryParams;
import com.datascope.app.model.vo.DataSourceVo;
import com.datascope.app.model.vo.IntegrationSourceVo;
import com.datascope.app.service.DatasourceService;
import com.datascope.app.service.IntegrationService;
import com.datascope.app.service.QueryService;
import com.datascope.facade.IntegrationQueryFacade;
import com.datascope.facade.dto.ParamSourceDTO;
import com.datascope.facade.dto.QueryParamDTO;
import com.datascope.facade.response.ResultResponse;
import com.yeepay.g3.core.yuia.yuiacommons.patron.RequiresGuest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 *
 * 外部接口管理控制器
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/external")
@Tag(name = "外部接口模块")
@Validated
public class ExternalController {

    private final IntegrationService integrationService;

    private final IntegrationQueryFacade integrationQueryFacade;

    private final DatasourceService datasourceService;

    private final QueryService queryService;

    @PostMapping("/config-update")
    @RepeatCommit(timeout = 1)
    @Operation(summary = "yop配置集成回调")
    @RequiresGuest
    public Response<Void> callbackIntegrationConfig(@Valid @RequestBody List<IntegrationCallBackDTO> request) {
        this.integrationService.callbackIntegrationConfig(request);
        return Response.ok();
    }

    @PostMapping("/a")
    @RequiresGuest
    public ResultResponse<Map<String, Object>> a(@RequestBody QueryParamDTO queryParamDTO) {
        ResultResponse<Map<String, Object>> query = this.integrationQueryFacade.query(queryParamDTO);
        return query;
    }

    @PostMapping("/usable-datasource-get")
    @RepeatCommit(timeout = 1)
    @Operation(summary = "查询可用数据源信息")
    @RequiresGuest
    public Response<List<DataSourceVo>> usableDatasourceGet(@RequestBody DataSourceQueryDTO queryDTO) {
        return Response.ok(this.datasourceService.usableDatasourceGet(queryDTO));
    }

    @PostMapping("/usable-query-get")
    @RepeatCommit(timeout = 1)
    @Operation(summary = "查询数据源下的可用查询")
    @RequiresGuest
    public Response<List<IntegrationSourceVo>> usableQueryGet(@RequestBody DataSourceQueryDTO queryDTO) {
        return Response.ok(this.integrationService.usableQueryGet(queryDTO));
    }

    @GetMapping("/param-get/{id}")
    @RepeatCommit(timeout = 1)
    @Operation(summary = "查询集成下的参数信息")
    @RequiresGuest
    public Response<ParamSourceDTO> paramGet(@PathVariable(value = "id") String id) {
        return Response.ok(this.integrationService.paramGet(id));
    }

    @GetMapping("/publish-check/{id}/{versionId}")
    @RepeatCommit(timeout = 1)
    @Operation(summary = "校验查询是否发生变化")
    public Response<Boolean> sqlCheck(@PathVariable(value = "id") String id,
                                      @PathVariable(value = "versionId") String versionId) {
        return Response.ok(queryService.checkChange(id, versionId, null));
    }

    @PostMapping("/integration-check/{id}")
    @RepeatCommit(timeout = 1)
    @Operation(summary = "校验集成参数是否发生变化")
    public Response<Boolean> paramCheck(@PathVariable(value = "id") String id,
                                        @RequestBody UpdateIntegrationRequest request) {
        return Response.ok(integrationService.paramCheck(id, request));
    }

    @PostMapping("/publish-check/{id}")
    @RepeatCommit(timeout = 1)
    @Operation(summary = "校验直接发布版本否发生变化")
    public Response<Boolean> sqlPublishCheck(@PathVariable(value = "id") String id,
                                             @RequestBody SaveQueryParams params) {
        return Response.ok(queryService.checkChange(id, null, params));
    }

    @GetMapping("/swagger-generate/{id}")
    @Operation(summary = "动态成Swagger JSON文档")
    @RequiresGuest
    public Response<String> generateSwaggerJson(@PathVariable(value = "id") String id) {
        Map<String, Object> map = this.integrationService.generateSwaggerJson(id);
        String json = JSONUtil.toJsonStr(map);
        return Response.ok(json);
    }
}
