package com.datascope.app.controller;

import com.datascope.app.common.response.Response;
import com.datascope.app.config.anno.RepeatCommit;
import com.datascope.app.service.impl.SynNoteImpl;
import com.yeepay.g3.core.yuia.yuiacommons.patron.RequiresAuthorization;
import com.yeepay.g3.core.yuia.yuiacommons.patron.RequiresGuest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 *
 * 外部接口管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/note")
@Tag(name = "外部接口模块")
@Validated
public class SynNoteController {

    @Autowired
    private SynNoteImpl synNote;


    @PostMapping("/sync-note")
    @RepeatCommit(timeout = 1)
    @Operation(summary = "同步笔记")
    @RequiresGuest
    @RequiresAuthorization(required = false)
    public Response<Void> synNote(@RequestBody List<String> loginNames) {

        this.synNote.syncNote(loginNames);
        return Response.ok();
    }

}
