package com.datascope.app.dto.datasource;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 数据源查询参数
 */
@Data
@Accessors(chain = true)
public class DataSourceQueryParam {

    /**
     * 名称（模糊查询）
     */
    private String name;

    /**
     * 数据源类型
     */
    private String type;

    /**
     * 状态
     */
    private String status;

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    private Integer size = 100;

    /**
     * 模块
     */
    private String moduleKey;

    private List<String> dataSourceNames;
}
