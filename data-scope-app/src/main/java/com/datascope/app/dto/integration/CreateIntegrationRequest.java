package com.datascope.app.dto.integration;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 创建集成请求
 */
@Data
@Accessors(chain = true)
public class CreateIntegrationRequest {

    /**
     * 集成ID
     */
    private String id;

    /**
     * 集成名称
     */
    @NotBlank(message = "集成名称不能为空")
    private String name;

    /**
     * 集成描述
     */
    private String description;

    /**
     * 集成类型：SIMPLE_TABLE-简单表格，TABLE-表格，CHART-图表
     */
    @NotBlank(message = "集成类型不能为空")
    private String type;

    /**
     * 状态：ACTIVE-活跃，INACTIVE-不活跃，DRAFT-草稿
     */
    private String status = "DRAFT";

    /**
     * 查询ID
     */
    @NotBlank(message = "查询ID不能为空")
    private String queryId;

    /**
     * 版本ID
     */
    private String versionId;

    /**
     * 数据源ID
     */
    @NotBlank(message = "数据源ID不能为空")
    private String dataSourceId;

    /**
     * 查询参数
     */
    private List<QueryParam> queryParams;

    /**
     * 表格配置
     */
    private Object tableConfig;

    /**
     * 图表配置
     */
    private Object chartConfig;

    /**
     * 元数据
     */
    private Object meta;

    /**
     * 集成点信息
     */
    private IntegrationPointInfo integrationPoint;

    /**
     * 必填配置
     */
    private Object requiredConfig;

    /**
     * 是否启用缓存
     */
    private Boolean enabledCache;

    /**
     * 查询参数定义
     */
    @Data
    @Accessors(chain = true)
    public static class QueryParam {
        /**
         * 参数名称
         */
        private String name;

        /**
         * 参数类型
         */
        private String type;

        /**
         * 参数描述
         */
        private String description;

        /**
         * 格式
         */
        private String format;

        /**
         * 表单类型
         */
        private String formType;

        /**
         * 是否必填
         */
        private Boolean required;

        /**
         * 显示顺序
         */
        private Integer displayOrder;

        /**
         * 默认值
         */
        private Object defaultValue;

        /**
         * 可选项
         */
        private List<Object> options;

        /**
         * 高级配置
         */
        private ExportConfig exportConfig;

        /**
         * 表名
         */
        private String tableName;

        /**
         * 枚举编码
         */
        private String enumCode;

        /**
         * 枚举名称
         */
        private String enumName;

        /**
         * 枚举ID
         */
        private String enumId;
    }

    /**
     * 高级配置
     */
    @Data
    @Accessors(chain = true)
    public static class ExportConfig {

        private Config config;
    }

    /**
     * 高级配置
     */
    @Data
    @Accessors(chain = true)
    public static class Config {

        /**
         * 模糊匹配
         */
        private Boolean isFuzzyMatch;

        /**
         * 多选
         */
        private Boolean isMultiSelect;

        private Boolean suspend;
    }


    /**
     * 集成点信息
     */
    @Data
    @Accessors(chain = true)
    public static class IntegrationPointInfo {
        /**
         * 集成点名称
         */
        private String name;

        /**
         * 集成点类型
         */
        private String type;

        /**
         * URL配置
         */
        private Object urlConfig;
    }
}
