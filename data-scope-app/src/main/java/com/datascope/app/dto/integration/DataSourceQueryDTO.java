package com.datascope.app.dto.integration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 * 数据源查询
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class DataSourceQueryDTO implements Serializable {

    private String loginName;

    private Long page;

    private Long size;

    private String datasourceId;

    public Long getPage() {
        if (page == null || page < 1) {
            return 1L;
        }
        return page;
    }

    public Long getSize() {
        if (size == null || size < 1) {
            return 20L;
        }
        return size;
    }
}
