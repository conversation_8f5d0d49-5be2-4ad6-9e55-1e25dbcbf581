package com.datascope.app.dto.integration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 *
 * 集成回调参数
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class IntegrationCallBackDTO {

    /**
     * 集成ID
     */
    @NotBlank(message = "集成ID不能为空")
    private String integrationId;

    /**
     * 回调引用类型
     */
    @NotBlank(message = "引用类型不能为空")
    private String type;
}


