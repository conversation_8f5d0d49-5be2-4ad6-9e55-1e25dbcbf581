package com.datascope.app.dto.integration;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 更新集成请求
 */
@Data
@Accessors(chain = true)
public class UpdateIntegrationRequest {

    /**
     * 集成ID
     */
    @NotBlank(message = "集成ID不能为空")
    private String id;

    /**
     * 集成名称
     */
    private String name;

    /**
     * 集成描述
     */
    private String description;

    /**
     * 集成类型：SIMPLE_TABLE-简单表格，TABLE-表格，CHART-图表
     */
    private String type;

    /**
     * 状态：ACTIVE-活跃，INACTIVE-不活跃，DRAFT-草稿
     */
    private String status;

    /**
     * 查询ID
     */
    private String queryId;

    /**
     * 版本ID
     */
    private String versionId;

    /**
     * 数据源ID
     */
    private String dataSourceId;

    /**
     * 查询参数
     */
    private List<CreateIntegrationRequest.QueryParam> queryParams;

    /**
     * 表格配置
     */
    private Object tableConfig;

    /**
     * 图表配置
     */
    private Object chartConfig;

    /**
     * 元数据
     */
    private Object meta;

    /**
     * 必填配置
     */
    private Object requiredConfig;

    /**
     * 集成点信息
     */
    private CreateIntegrationRequest.IntegrationPointInfo integrationPoint;

    /**
     * 是否启用缓存
     */
    private Boolean enabledCache;
}
