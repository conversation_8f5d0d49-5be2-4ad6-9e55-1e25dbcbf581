package com.datascope.app.dto.metadata;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * 表索引信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TableIndexDTO {

    /**
     * 索引名称
     */
    private String indexName;

    /**
     * 索引类型（如：BTREE, HASH等）
     */
    private String indexType;

    /**
     * 是否唯一索引
     */
    private Boolean isUnique;

    /**
     * 是否主键索引
     */
    private Boolean isPrimary;

    /**
     * 索引字段列表
     */
    private List<IndexColumnDTO> columns;

    /**
     * 索引列数量
     */
    private Integer columnCount;

    /**
     * 索引描述
     */
    private String description;

    /**
     * 原始索引类型值
     */
    private String rawIndexType;

    /**
     * 数据库类型
     */
    private String databaseType;

    /**
     * 索引列信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndexColumnDTO {
        /**
         * 列名
         */
        private String columnName;

        /**
         * 列位置（从1开始）
         */
        private Integer position;

        /**
         * 排序方式（ASC/DESC）
         */
        private String order;

        /**
         * 是否为主列
         */
        private Boolean isPrimary;
    }
}
