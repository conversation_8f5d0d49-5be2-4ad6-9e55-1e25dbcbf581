package com.datascope.app.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;

/**
 * 执行历史查询参数DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionHistoryQueryParam {

    /**
     * 数据源名称
     */
    private String dataSourceName;

    /**
     * Schema名称
     */
    private String schemaName;

    /**
     * 执行人
     */
    private String executedBy;

    /**
     * 执行时间开始（年月日）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date executedAtStart;

    /**
     * 执行时间结束（年月日）
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date executedAtEnd;

    /**
     * 状态：success-成功，error-错误，running-运行中，cancelled-已取消
     */
    private String status;

    /**
     * 查询ID
     */
    private String queryId;

    /**
     * 分页页码
     */
    private Integer page;

    /**
     * 分页大小
     */
    private Integer size;

    /**
     * 排序字段
     */
    private String sortBy;

    /**
     * 排序方向：asc-升序，desc-降序
     */
    private String sortDir;
}
