package com.datascope.app.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 查询版本DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryVersionDTO {

    /**
     * 版本ID
     */
    private String id;

    /**
     * 查询ID
     */
    private String queryId;

    /**
     * 版本号
     */
    private Integer versionNumber;

    /**
     * 版本名称
     */
    private String name;

    /**
     * 版本描述
     */
    private String description;

    /**
     * SQL内容
     */
    private String sql;

    /**
     * 数据源ID
     */
    private String dataSourceId;

    private String schemaId;

    /**
     * 数据源信息
     */
    private DataSourceInfo dataSource;

    /**
     * 参数列表
     */
    private List<QueryParameterDTO> parameters;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否最新版本
     */
    private Boolean isLatest;

    /**
     * 创建人信息
     */
    private QueryDTO.UserInfo createdBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    /**
     * 更新人信息
     */
    private QueryDTO.UserInfo updatedBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 版本注释
     */
    private String comment;

    /**
     * 数据源信息DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataSourceInfo {
        /**
         * 数据源ID
         */
        private String id;

        /**
         * 数据源名称
         */
        private String name;

        /**
         * 数据源类型
         */
        private String type;

        /**
         * 数据源图标
         */
        private String icon;
    }
}
