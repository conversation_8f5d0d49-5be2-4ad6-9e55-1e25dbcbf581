package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.baomidou.mybatisplus.annotation.Version;

import java.util.Date;

/**
 * 查询版本实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("query_versions")
public class QueryVersion {

    /**
     * 版本ID
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 查询ID
     */
    @TableField("query_id")
    private String queryId;

    /**
     * 版本号
     */
    @TableField("version_number")
    private Integer versionNumber;

    /**
     * 版本名称
     */
    @TableField("name")
    private String name;

    /**
     * 版本描述
     */
    @TableField("description")
    private String description;

    /**
     * SQL内容
     */
    @TableField("sql_content")
    private String sqlContent;

    /**
     * 数据源ID
     */
    @TableField("data_source_id")
    private String dataSourceId;

    /**
     * 状态：DRAFT-草稿，PUBLISHED-已发布，DEPRECATED-已废弃，ARCHIVED-已归档
     */
    @TableField("status")
    private String status;

    /**
     * 是否最新版本
     */
    @TableField("is_latest")
    private Boolean isLatest;

    /**
     * 版本注释
     */
    @TableField("comment")
    private String comment;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 乐观锁
     */
    @Version
    @TableField("nonce")
    private Integer nonce;

    @TableField("schema_id")
    private String schemaId;
}
