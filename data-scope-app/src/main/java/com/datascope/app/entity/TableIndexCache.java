package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 表索引信息缓存实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@Accessors(chain = true)
@TableName("table_index_cache")
@NoArgsConstructor
@AllArgsConstructor
public class TableIndexCache extends BaseEntity {

    /**
     * 表ID
     */
    @TableField("table_id")
    private String tableId;

    /**
     * 数据源ID
     */
    @TableField("datasource_id")
    private String datasourceId;

    /**
     * 索引名称
     */
    @TableField("index_name")
    private String indexName;

    /**
     * 索引类型
     */
    @TableField("index_type")
    private String indexType;

    /**
     * 是否唯一索引
     */
    @TableField("is_unique")
    private Integer isUnique;

    /**
     * 是否主键索引
     */
    @TableField("is_primary")
    private Integer isPrimary;

    /**
     * 索引列数量
     */
    @TableField("column_count")
    private Integer columnCount;

    /**
     * 索引描述
     */
    @TableField("description")
    private String description;

    /**
     * 原始索引类型值
     */
    @TableField("raw_index_type")
    private String rawIndexType;

    /**
     * 数据库类型
     */
    @TableField("database_type")
    private String databaseType;

    /**
     * 最后同步时间
     */
    @TableField("last_sync_time")
    private LocalDateTime lastSyncTime;

    /**
     * 同步状态
     */
    @TableField("sync_status")
    private String syncStatus;

    /**
     * 同步错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 关联的列信息（非数据库字段）
     */
    @TableField(exist = false)
    private List<TableIndexColumnCache> columns;
}
