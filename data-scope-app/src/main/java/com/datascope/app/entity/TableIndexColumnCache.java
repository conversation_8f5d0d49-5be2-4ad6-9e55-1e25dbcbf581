package com.datascope.app.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 表索引列信息缓存实体
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("table_index_column_cache")
public class TableIndexColumnCache extends BaseEntity {

    /**
     * 索引缓存ID
     */
    @TableField("index_cache_id")
    private String indexCacheId;

    /**
     * 列名
     */
    @TableField("column_name")
    private String columnName;

    /**
     * 列位置
     */
    @TableField("position")
    private Integer position;

    /**
     * 排序方式
     */
    @TableField("sort_order")
    private String sortOrder;

    /**
     * 是否主列
     */
    @TableField("is_primary_column")
    private Integer isPrimaryColumn;
}
