package com.datascope.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datascope.app.entity.TableIndexCache;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表索引缓存Mapper接口
 */
@Mapper
public interface TableIndexCacheMapper extends BaseMapper<TableIndexCache> {

    /**
     * 根据表ID查询索引缓存
     */
    List<TableIndexCache> selectByTableId(@Param("tableId") String tableId);

    /**
     * 删除表的索引缓存
     */
    int deleteByTableId(@Param("tableId") String tableId);

    /**
     * 批量插入索引缓存
     */
    int batchInsert(@Param("list") List<TableIndexCache> indexCaches);
}
