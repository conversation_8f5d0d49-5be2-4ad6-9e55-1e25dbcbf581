package com.datascope.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.datascope.app.entity.TableIndexColumnCache;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表索引列缓存Mapper接口
 */
@Mapper
public interface TableIndexColumnCacheMapper extends BaseMapper<TableIndexColumnCache> {

    /**
     * 根据索引缓存ID查询列信息
     */
    List<TableIndexColumnCache> selectByIndexCacheId(@Param("indexCacheId") String indexCacheId);

    /**
     * 删除索引的列缓存
     */
    int deleteByIndexCacheId(@Param("indexCacheId") String indexCacheId);

    /**
     * 批量插入列缓存
     */
    int batchInsert(@Param("list") List<TableIndexColumnCache> columnCaches);

    /**
     * 批量删除索引的列缓存
     */
    int batchDeleteByIndexCacheIds(@Param("indexCacheIds") List<String> indexCacheIds);
}
