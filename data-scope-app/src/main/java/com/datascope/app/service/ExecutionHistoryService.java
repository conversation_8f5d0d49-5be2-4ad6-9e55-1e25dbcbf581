package com.datascope.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datascope.app.dto.query.ExecutionHistoryDTO;
import com.datascope.app.dto.query.ExecutionHistoryQueryParam;

/**
 * 执行历史服务接口
 */
public interface ExecutionHistoryService {

    /**
     * 获取执行历史列表
     *
     * @param queryParam 查询参数
     * @return 执行历史分页结果
     */
    Page<ExecutionHistoryDTO> getExecutionHistory(ExecutionHistoryQueryParam queryParam);

    /**
     * 获取执行历史详情
     *
     * @param id 执行历史ID
     * @return 执行历史详情
     */
    ExecutionHistoryDTO getExecutionHistoryDetail(String id);
}
