package com.datascope.app.service;

import com.datascope.app.common.response.PageResponse;
import com.datascope.app.dto.integration.CreateIntegrationRequest;
import com.datascope.app.dto.integration.DataSourceQueryDTO;
import com.datascope.app.dto.integration.ExecuteIntegrationQueryRequest;
import com.datascope.app.dto.integration.IntegrationCallBackDTO;
import com.datascope.app.dto.integration.IntegrationDTO;
import com.datascope.app.dto.integration.IntegrationQueryParam;
import com.datascope.app.dto.integration.IntegrationQueryResultDTO;
import com.datascope.app.dto.integration.UpdateIntegrationRequest;
import com.datascope.app.dto.integration.UpdateIntegrationStatusRequest;
import com.datascope.app.model.vo.IntegrationSourceVo;
import com.datascope.facade.dto.ParamSourceDTO;

import java.util.List;
import java.util.Map;

/**
 * 集成管理服务接口
 */
public interface IntegrationService {

    /**
     * 获取集成列表
     *
     * @param param 查询参数
     * @return 集成列表
     */
    PageResponse<IntegrationDTO> getIntegrationList(IntegrationQueryParam param);

    /**
     * 创建集成
     *
     * @param request 创建请求
     * @return 创建后的集成信息
     */
    IntegrationDTO createIntegration(CreateIntegrationRequest request);

    /**
     * 根据ID获取集成详情
     *
     * @param id 集成ID
     * @return 集成详情
     */
    IntegrationDTO getIntegrationById(String id);

    /**
     * 更新集成
     *
     * @param request 更新请求
     * @return 更新后的集成信息
     */
    IntegrationDTO updateIntegration(UpdateIntegrationRequest request);

    /**
     * 删除集成
     *
     * @param id 集成ID
     * @return 是否删除成功
     */
    boolean deleteIntegration(String id);

    /**
     * 预览集成
     *
     * @param id 集成ID
     * @return 预览数据
     */
    IntegrationQueryResultDTO previewIntegration(String id);

    /**
     * 更新集成状态
     *
     * @param id 集成ID
     * @param request 状态更新请求
     * @return 更新后的状态信息
     */
    IntegrationDTO updateIntegrationStatus(String id, UpdateIntegrationStatusRequest request);

    /**
     * 执行集成查询
     *
     * @param request 执行请求
     * @return 查询结果
     */
    IntegrationQueryResultDTO executeIntegrationQuery(ExecuteIntegrationQueryRequest request);

    /**
     * 更新集成URL
     *
     * @param integrationId 集成ID
     * @param url 集成URL
     */
    void updateIntegrationUrl(String integrationId, String url);

    /**
     * 回调集成配置
     *
     * @param integrationCallBackDTOList 集成信息
     */
    void callbackIntegrationConfig(List<IntegrationCallBackDTO> integrationCallBackDTOList);

    /**
     * 获取可用的集成列表
     *
     * @param dataSourceQueryDTO dataSourceQueryDTO
     * @return List
     */
    List<IntegrationSourceVo> usableQueryGet(DataSourceQueryDTO dataSourceQueryDTO);

    /**
     * 获取集成参数信息
     *
     * @param id id
     * @return ParamSourceDTO
     */
    ParamSourceDTO paramGet(String id);

    /**
     * 校验集成参数是否发生变化
     *
     * @param id id
     * @param request request
     * @return Boolean
     */
    Boolean paramCheck(String id, UpdateIntegrationRequest request);

    /**
     * 动态生成Swagger JSON文档
     *
     * @param id 集成ID
     * @return Swagger JSON文档
     */
    Map<String, Object> generateSwaggerJson(String id);
}
