package com.datascope.app.service;

import com.datascope.app.common.enums.AuthTypeEnum;
import com.datascope.app.dto.metadata.AuthDTO;
import com.datascope.app.dto.metadata.ColumnDTO;
import com.datascope.app.dto.metadata.SchemaDTO;
import com.datascope.app.dto.metadata.SyncMetadataRequest;
import com.datascope.app.dto.metadata.SyncMetadataResponse;
import com.datascope.app.dto.metadata.TableDTO;
import com.datascope.app.dto.metadata.TableDataQueryRequest;
import com.datascope.app.dto.metadata.TableDataResponse;
import com.datascope.app.dto.metadata.TableIndexDTO;

import java.util.List;

/**
 * 元数据服务接口
 */
public interface MetadataService {

    /**
     * 同步数据源元数据
     *
     * @param dataSourceId 数据源ID
     * @param request 同步请求参数
     * @return 同步结果
     */
    SyncMetadataResponse syncMetadata(String dataSourceId, SyncMetadataRequest request);

    /**
     * 获取数据源的schema列表
     *
     * @param dataSourceId 数据源ID
     * @return Schema列表
     */
    List<SchemaDTO> getSchemas(String dataSourceId);

    /**
     * 获取schema的表列表
     *
     * @param schemaId Schema ID
     * @return 表列表
     */
    List<TableDTO> getTables(String schemaId);

    /**
     * 获取表的列列表
     *
     * @param tableId 表ID
     * @return 列列表
     */
    List<ColumnDTO> getColumns(String tableId);

    /**
     * 分页查询表数据
     *
     * @param tableId 表ID
     * @param request 查询参数
     * @return 表数据
     */
    TableDataResponse getTableData(String tableId, TableDataQueryRequest request);

    /**
     * 获取表的索引结构
     *
     * @param tableId 表ID
     * @return 索引列表
     */
    List<TableIndexDTO> getTableIndexes(String tableId);

    /**
     * 刷新表的索引结构
     *
     * @param tableId 表ID
     * @return 刷新后的索引列表
     */
    List<TableIndexDTO> refreshTableIndexes(String tableId);

    /**
     * 更新元数据
     *
     * @param columnId columnId
     * @param param param
     */
    void updateColumnEntry(String columnId, Object param);

    /**
     * 更新字段解密配置
     *
     * @param columnId 字段ID
     * @param decryptConfig 解密配置
     */
    void updateColumnDecryptConfig(String columnId, Object decryptConfig);

    /**
     * 授权信息
     *
     * @param authDTO authDTO
     */
    void authMetaData(AuthDTO authDTO);

    /**
     * authDesensitize
     *
     * @param authDTO authDTO
     */
    void authDesensitize(AuthDTO authDTO);

    /**
     * 获取授权类型
     *
     * @param type 类型
     * @return 授权类型
     */
    AuthTypeEnum getAuthType(String type);

    /**
     * 根据AuthDTO获取对应的数据源ID（用于权限校验）
     *
     * @param authDTO 授权信息
     * @return 数据源ID
     */
    String getDatasourceIdByAuthDTO(AuthDTO authDTO);
}
