package com.datascope.app.service;

import com.datascope.app.dto.integration.IntegrationQueryResultDTO;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 查询缓存服务
 * 用于缓存查询结果，提高高并发场景下的响应速度
 *
 * <AUTHOR>
 */
public interface QueryCacheService {

    /**
     * 获取缓存的查询结果
     *
     * @param cacheKey 缓存键
     * @return 查询结果，如果不存在则返回null
     */
    List<Map<String, Object>> getCachedResult(String cacheKey);

    /**
     * 异步获取缓存的查询结果
     *
     * @param cacheKey 缓存键
     * @return 异步查询结果
     */
    CompletableFuture<List<Map<String, Object>>> getCachedResultAsync(String cacheKey);

    /**
     * 缓存查询结果
     *
     * @param cacheKey 缓存键
     * @param result 查询结果
     * @param expireSeconds 过期时间（秒）
     */
    void cacheResult(String cacheKey, List<Map<String, Object>> result, int expireSeconds);

    /**
     * 异步缓存查询结果
     *
     * @param cacheKey 缓存键
     * @param result 查询结果
     * @param expireSeconds 过期时间（秒）
     */
    CompletableFuture<Void> cacheResultAsync(String cacheKey, List<Map<String, Object>> result, int expireSeconds);

    /**
     * 生成缓存键
     *
     * @param integrationId 集成ID
     * @param parameters 查询参数
     * @return 缓存键
     */
    String generateCacheKey(String integrationId, Map<String, Object> parameters);

    /**
     * 删除缓存
     *
     * @param cacheKey 缓存键
     */
    void evictCache(String cacheKey);

    /**
     * 批量删除缓存
     *
     * @param cacheKeys 缓存键列表
     */
    void evictCacheBatch(java.util.List<String> cacheKeys);


    /**
     * 生成SQL缓存键
     *
     * @param sql sql
     * @return String
     */
    String generateCacheKey(String sql);

    /**
     * 清空所有缓存
     */
    void clearAllCache();
}
