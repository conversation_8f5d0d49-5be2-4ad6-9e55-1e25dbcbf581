package com.datascope.app.service.facade;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.datascope.app.common.config.DynamicDataSourcePoolManager;
import com.datascope.app.common.response.DubboCheckException;
import com.datascope.app.config.anno.QueryRateLimit;
import com.datascope.app.constants.Constant;
import com.datascope.app.dto.integration.IntegrationDTO;
import com.datascope.app.dto.query.ExecuteQueryParams;
import com.datascope.app.dto.query.QueryCheckDTO;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.QueryVersion;
import com.datascope.app.entity.Schema;
import com.datascope.app.mapper.DatasourceMapper;
import com.datascope.app.mapper.QueryVersionMapper;
import com.datascope.app.mapper.SchemaMapper;
import com.datascope.app.model.PaginationResult;
import com.datascope.app.model.TableConfigBO;
import com.datascope.app.service.IntegrationService;
import com.datascope.app.service.QueryCacheService;
import com.datascope.app.service.QueryService;
import com.datascope.app.util.SizeCalculator;
import com.datascope.facade.IntegrationQueryFacade;
import com.datascope.facade.dto.ParamSourceDTO;
import com.datascope.facade.dto.QueryParamDTO;
import com.datascope.facade.response.ResultResponse;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.yeepay.g3.utils.config.ConfigurationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;


/**
 * 集成查询Dubbo接口实现
 * 高性能、高可靠性的查询服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class IntegrationQueryFacadeImpl implements IntegrationQueryFacade {

    @Autowired
    private IntegrationService integrationService;

    @Autowired
    private QueryCacheService queryCacheService;

    @Resource(name = "dubboQueryExecutor")
    private ThreadPoolExecutor dubboQueryExecutor;

    @Autowired
    private QueryVersionMapper queryVersionMapper;

    @Autowired
    private QueryService queryService;

    @Autowired
    private DatasourceMapper datasourceMapper;

    @Autowired
    private DynamicDataSourcePoolManager poolManager;

    @Autowired
    private SchemaMapper schemaMapper;

    @Autowired
    private ObjectMapper objectMapper;

    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    @QueryRateLimit
    public ResultResponse<Map<String, Object>> query(QueryParamDTO paramDTO) {
        long startTime = System.currentTimeMillis();
        String cacheKey = "";
        try {
            // 参数校验
            Assert.isTrue(paramDTO != null && StrUtil.isNotBlank(paramDTO.getId()), () -> new DubboCheckException("参数无效: 查询集成ID不能为空"));

            String integrationId = paramDTO.getId();
            Map<String, Object> parameters = paramDTO.getParams();
            Integer page = paramDTO.getPage();
            Integer size = paramDTO.getSize();
            log.info("开始执行dubbo集成查询: integrationId={}, page={}, size={}, params={}", integrationId, page, size, parameters);

            // 校验参数信息
            IntegrationDTO integration = integrationService.getIntegrationById(integrationId);
            checkData(integration, integrationId);
            // 获取最新发布的查询版本
            QueryVersion queryVersion = queryVersionMapper.selectOne(new LambdaQueryWrapper<>(QueryVersion.class)
                .eq(QueryVersion::getQueryId, integration.getQueryId())
                .eq(QueryVersion::getStatus, "PUBLISHED")
                .orderByDesc(QueryVersion::getVersionNumber)
                .last("LIMIT 1"));
            Assert.isTrue(queryVersion != null, ()-> new DubboCheckException("集成对应查询没有已发布的版本"));
            Assert.isTrue(StrUtil.isNotBlank(queryVersion.getSqlContent()), () -> new DubboCheckException("查询对应的SQL不能为空"));
            Datasource datasource = datasourceMapper.selectById(queryVersion.getDataSourceId());
            Assert.isTrue(datasource != null, () -> new DubboCheckException("数据源不存在"));
            QueryCheckDTO queryCheckDTO = queryService.convertSql(queryVersion.getSqlContent(), datasource,
                ExecuteQueryParams.builder().parameters(parameters).build(), integration);
            String appendSql = queryCheckDTO.getSql();
            log.info("dubbo append SQL: {}", appendSql);

            // 智能分页处理
            PaginationResult paginationResult = queryService.applySmartPagination(null, appendSql,
                ExecuteQueryParams.builder().page(page).size(size).build(), datasource.getType(), false);
            String finalSql = paginationResult.getSql();
            log.info("dubbo finalSql SQL: {}", finalSql);

            // 检查缓存
            cacheKey = queryCacheService.generateCacheKey(finalSql);
            log.info("缓存Key: {}", cacheKey);
            List<Map<String, Object>> cachedResult = queryCacheService.getCachedResult(cacheKey);
            if (cachedResult != null) {
                log.info("返回缓存结果: integrationId={}, cachedResult={}", integrationId, cachedResult);
                return ResultResponse.success(cachedResult);
            }

            // 异步执行查询
            CompletableFuture<List<Map<String, Object>>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    // 构建查询请求
                    return this.queryData(datasource, queryVersion.getSchemaId(), finalSql);
                } catch (Exception e) {
                    log.error("查询执行异常: integrationId={}", integrationId, e);
                    throw new RuntimeException("查询执行失败: " + e.getMessage(), e);
                }
            }, dubboQueryExecutor);

            // 等待查询结果（设置超时）
            List<Map<String, Object>> result;
            try {
                result = future.get(1, TimeUnit.MINUTES);
                log.info("查询执行成功: integrationId={}, result={}", integrationId, result);
            } catch (TimeoutException e) {
                log.warn("查询执行超时: integrationId={}, 尝试返回缓存数据", integrationId);
                // 超时降级：尝试返回缓存数据
                List<Map<String, Object>> cachedData = queryCacheService.getCachedResult(cacheKey);
                if (cachedData != null) {
                    log.info("超时降级成功：返回缓存数据");
                    return ResultResponse.success(cachedData);
                }
                throw new RuntimeException("查询执行超时，且无缓存数据");
            }

            processResult(result, integration);
            // 记录查询完成
            long executionTime = System.currentTimeMillis() - startTime;
            int rowCount = result != null && !result.isEmpty() ? result.size() : 0;

            log.info("dubbo集成查询执行成功: integrationId={}, executionTime={}ms, rowCount={}",
                integrationId, executionTime, rowCount);

            // 异步缓存结果
            if (integration.getEnabledCache()) {
                // 检查返回值大小，如果不超过3M才做缓存
                if (!SizeCalculator.isSizeExceeded(result)) {
                    queryCacheService.cacheResultAsync(cacheKey, result, getCacheTime(integration.getCacheTime()));
                    log.info("查询结果已加入缓存: integrationId={}, size={} bytes", integrationId, SizeCalculator.calculateSize(result));
                } else {
                    log.info("查询结果大小超过缓存限制，跳过缓存: integrationId={}, size={} bytes",
                        integrationId, SizeCalculator.calculateSize(result));
                }
            }
            return ResultResponse.success(result);

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            String errorMessage = "查询执行失败: " + e.getMessage();

            // 异常降级：尝试返回缓存数据
            try {
                List<Map<String, Object>> cachedResult = queryCacheService.getCachedResult(cacheKey);
                if (cachedResult != null) {
                    log.info("异常降级成功：返回缓存数据, integrationId={}", paramDTO.getId());
                    return new ResultResponse<>(true, "0", "查询异常，返回缓存数据", null);
                }
            } catch (Exception cacheEx) {
                log.warn("获取缓存数据失败: integrationId={}", paramDTO.getId(), cacheEx);
            }

            log.error("集成查询执行异常: integrationId={}, executionTime={}ms",
                paramDTO != null ? paramDTO.getId() : "unknown", executionTime, e);

            return new ResultResponse<>(false, "500", errorMessage, null);
        }
    }

    private void checkData(IntegrationDTO integration, String integrationId) {
        Assert.isTrue(Objects.nonNull(integration), () -> new DubboCheckException("集成不存在: " + integrationId));
        Assert.isTrue(integration.getStatus() != null, () -> new DubboCheckException("集成状态异常: " + integrationId));
        Assert.isTrue(integration.getDataSourceId() != null, () -> new DubboCheckException("集成数据源不存在: " + integrationId));
        Assert.isTrue(integration.getQueryId() != null, () -> new DubboCheckException("集成查询不存在: " + integrationId));
    }

    private void processResult(List<Map<String, Object>> result, IntegrationDTO integrationDTO) {
        if (result == null || result.isEmpty()) {
            return;
        }

        Object tableConfig = integrationDTO.getTableConfig();
        List<TableConfigBO> configBOList = Lists.newArrayList();
        try {
            JsonNode jsonNode = objectMapper.readTree(objectMapper.writeValueAsString(tableConfig));
            if (Objects.nonNull(jsonNode) && !jsonNode.isEmpty()) {
                JsonNode node = jsonNode.get("columns");
                if (Objects.nonNull(node) && node.isArray()) {
                    configBOList = objectMapper.readerForListOf(TableConfigBO.class).readValue(node);
                }
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        List<String> fields = configBOList.stream().map(TableConfigBO::getField).collect(Collectors.toList());
        log.info("配置的列个数: {}, 元素: {}", fields.size(), fields);
        result.forEach(row -> {
            // 移除多余字段
            row.keySet().removeIf(key -> !fields.contains(key));
            // 补齐缺失字段
            fields.forEach(field -> row.putIfAbsent(field, null));
        });

        for (Map<String, Object> map : result) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                if (entry.getValue() instanceof Date) {
                    entry.setValue(SIMPLE_DATE_FORMAT.format((Date) entry.getValue()));
                } else if (entry.getValue() instanceof LocalDateTime) {
                    entry.setValue(SIMPLE_DATE_FORMAT.format(Date.from(((LocalDateTime) entry.getValue()).atZone(ZoneId.systemDefault()).toInstant())));
                }
            }
        }
    }

    private List<Map<String, Object>> queryData(Datasource datasource, String schemaId, String sql) {
        // 执行SQL查询
        String schemaName;
        if (StrUtil.isEmpty(schemaId)) {
            if (StrUtil.isNotBlank(datasource.getSchema())) {
                schemaName = datasource.getSchema();
            } else {
                schemaName = datasource.getDatabaseName();
            }
        } else {
            Schema schema = schemaMapper.selectById(schemaId);
            if (schema != null) {
                schemaName = schema.getName();
            } else if (StrUtil.isNotBlank(datasource.getSchema())) {
                schemaName = datasource.getSchema();
            } else {
                schemaName = datasource.getDatabaseName();
            }
        }
        datasource.setSchema(schemaName);
        JdbcTemplate jdbcTemplate = poolManager.getJdbcTemplate(datasource, schemaName);
        List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);
        log.info("dubbo查询结果: rows={}", rows);
        return rows;
    }

    @Override
    public ResultResponse<ParamSourceDTO> paramGet(String id) {
        ParamSourceDTO paramSourceVo = this.integrationService.paramGet(id);
        return ResultResponse.success(Lists.newArrayList(paramSourceVo));
    }

    private Integer getCacheTime(Integer cacheTime) {
        if (cacheTime != null && cacheTime > 0) {
            return cacheTime;
        }
        // from config center
        Integer value = null;
        String cacheTimeCenter = Constant.ConfigCenter.DATA_SCOPE_INTEGRATION_CACHE_TIME;
        try {
            Object object = ConfigurationUtils.getSysConfigParam(cacheTimeCenter).getValue();
            if (object instanceof Integer) {
                value = (Integer) object;
            } else {
                log.warn("from config center {} fail, use default value：{}", cacheTimeCenter, 300);
            }
        } catch (Exception e) {
            log.warn("from config center fail: ", e);
        }
        return Objects.isNull(value) ? 300 : value;
    }

}
