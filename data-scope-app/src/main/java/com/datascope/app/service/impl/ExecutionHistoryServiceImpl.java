package com.datascope.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datascope.app.dto.query.ExecutionHistoryDTO;
import com.datascope.app.dto.query.ExecutionHistoryQueryParam;
import com.datascope.app.entity.ExecutionHistory;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Schema;
import com.datascope.app.mapper.ExecutionHistoryMapper;
import com.datascope.app.mapper.DatasourceMapper;
import com.datascope.app.mapper.SchemaMapper;
import com.datascope.app.service.ExecutionHistoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Collections;
import java.util.Calendar;

/**
 * <AUTHOR>
 *
 * 执行历史服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExecutionHistoryServiceImpl implements ExecutionHistoryService {

    private final ExecutionHistoryMapper executionHistoryMapper;
    private final DatasourceMapper datasourceMapper;
    private final SchemaMapper schemaMapper;

    @Override
    public Page<ExecutionHistoryDTO> getExecutionHistory(ExecutionHistoryQueryParam queryParam) {
        log.info("查询执行历史列表: {}", queryParam);

        // 构建查询条件
        LambdaQueryWrapper<ExecutionHistory> queryWrapper = new LambdaQueryWrapper<>();

        // 根据数据源名称查询数据源ID
        if (StringUtils.isNotBlank(queryParam.getDataSourceName())) {
            LambdaQueryWrapper<Datasource> datasourceWrapper = new LambdaQueryWrapper<>();
            datasourceWrapper.eq(Datasource::getName, queryParam.getDataSourceName());
            List<Datasource> datasources = datasourceMapper.selectList(datasourceWrapper);
            if (CollUtil.isNotEmpty(datasources)) {
                queryWrapper.in(ExecutionHistory::getDatasourceId, datasources.stream().map(Datasource::getId).collect(Collectors.toList()));
            } else {
                // 如果数据源不存在，返回空结果
                log.warn("数据源不存在: {}", queryParam.getDataSourceName());
                return new Page<>();
            }
        }

        // 根据Schema名称查询Schema ID
        if (StringUtils.isNotBlank(queryParam.getSchemaName())) {
            LambdaQueryWrapper<Schema> schemaWrapper = new LambdaQueryWrapper<>();
            schemaWrapper.eq(Schema::getName, queryParam.getSchemaName());
            List<Schema> schemas = schemaMapper.selectList(schemaWrapper);
            if (CollUtil.isNotEmpty(schemas)) {
                queryWrapper.in(ExecutionHistory::getSchemaId, schemas.stream().map(Schema::getId).collect(Collectors.toList()));
            } else {
                // 如果Schema不存在，返回空结果
                log.warn("Schema不存在: {}", queryParam.getSchemaName());
                return new Page<>();
            }
        }

        // 执行人
        if (StringUtils.isNotBlank(queryParam.getExecutedBy())) {
            queryWrapper.eq(ExecutionHistory::getExecutedBy, queryParam.getExecutedBy());
        }

        // 执行时间范围（处理日期到时分秒的转换）
        if (queryParam.getExecutedAtStart() != null) {
            // 开始时间：当天00:00:00
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(queryParam.getExecutedAtStart());
            startCal.set(Calendar.HOUR_OF_DAY, 0);
            startCal.set(Calendar.MINUTE, 0);
            startCal.set(Calendar.SECOND, 0);
            startCal.set(Calendar.MILLISECOND, 0);
            queryWrapper.ge(ExecutionHistory::getExecutedAt, startCal.getTime());
        }
        if (queryParam.getExecutedAtEnd() != null) {
            // 结束时间：当天23:59:59
            Calendar endCal = Calendar.getInstance();
            endCal.setTime(queryParam.getExecutedAtEnd());
            endCal.set(Calendar.HOUR_OF_DAY, 23);
            endCal.set(Calendar.MINUTE, 59);
            endCal.set(Calendar.SECOND, 59);
            endCal.set(Calendar.MILLISECOND, 999);
            queryWrapper.le(ExecutionHistory::getExecutedAt, endCal.getTime());
        }

        // 状态
        if (StringUtils.isNotBlank(queryParam.getStatus())) {
            queryWrapper.eq(ExecutionHistory::getStatus, queryParam.getStatus());
        }

        // 查询ID
        if (StringUtils.isNotBlank(queryParam.getQueryId())) {
            queryWrapper.eq(ExecutionHistory::getQueryId, queryParam.getQueryId());
        }

        // 排序
        queryWrapper.orderByDesc(ExecutionHistory::getExecutedAt);

        // 分页查询
        Page<ExecutionHistory> page = new Page<>(queryParam.getPage(), queryParam.getSize());
        Page<ExecutionHistory> result = executionHistoryMapper.selectPage(page, queryWrapper);

        // 转换为DTO
        List<ExecutionHistoryDTO> dtoList = convertToDTOList(result.getRecords());

        // 构建返回结果
        Page<ExecutionHistoryDTO> dtoPage = new Page<>();
        dtoPage.setRecords(dtoList);
        dtoPage.setTotal(result.getTotal());
        dtoPage.setPages(result.getPages());
        dtoPage.setCurrent(result.getCurrent());
        dtoPage.setSize(result.getSize());

        return dtoPage;
    }

    @Override
    public ExecutionHistoryDTO getExecutionHistoryDetail(String id) {
        log.info("获取执行历史详情: {}", id);

        ExecutionHistory executionHistory = executionHistoryMapper.selectById(id);
        if (executionHistory == null) {
            throw new RuntimeException("执行历史不存在: " + id);
        }

        return convertToDTO(executionHistory);
    }

    /**
     * 批量转换为DTO，优化性能
     */
    private List<ExecutionHistoryDTO> convertToDTOList(List<ExecutionHistory> executionHistories) {
        if (executionHistories == null || executionHistories.isEmpty()) {
            return Collections.emptyList();
        }

        // 收集所有需要查询的数据源ID和Schema ID
        Set<String> datasourceIds = new HashSet<>();
        Set<String> schemaIds = new HashSet<>();

        for (ExecutionHistory history : executionHistories) {
            if (StringUtils.isNotBlank(history.getDatasourceId())) {
                datasourceIds.add(history.getDatasourceId());
            }
            if (StringUtils.isNotBlank(history.getSchemaId())) {
                schemaIds.add(history.getSchemaId());
            }
        }

        // 批量查询数据源名称
        final Map<String, String> datasourceNameMap = new HashMap<>();
        if (!datasourceIds.isEmpty()) {
            LambdaQueryWrapper<Datasource> datasourceWrapper = new LambdaQueryWrapper<>();
            datasourceWrapper.in(Datasource::getId, datasourceIds);
            List<Datasource> datasources = datasourceMapper.selectList(datasourceWrapper);
            datasourceNameMap.putAll(datasources.stream()
                .collect(Collectors.toMap(Datasource::getId, Datasource::getName)));
        }

        // 批量查询Schema名称
        final Map<String, String> schemaNameMap = new HashMap<>();
        if (!schemaIds.isEmpty()) {
            LambdaQueryWrapper<Schema> schemaWrapper = new LambdaQueryWrapper<>();
            schemaWrapper.in(Schema::getId, schemaIds);
            List<Schema> schemas = schemaMapper.selectList(schemaWrapper);
            schemaNameMap.putAll(schemas.stream()
                .collect(Collectors.toMap(Schema::getId, Schema::getName)));
        }

        // 转换DTO
        return executionHistories.stream()
            .map(history -> convertToDTO(history, datasourceNameMap, schemaNameMap))
            .collect(Collectors.toList());
    }

    /**
     * 转换为DTO
     */
    private ExecutionHistoryDTO convertToDTO(ExecutionHistory executionHistory) {
        return convertToDTO(executionHistory, new HashMap<>(), new HashMap<>());
    }

    /**
     * 转换为DTO（带缓存）
     */
    private ExecutionHistoryDTO convertToDTO(ExecutionHistory executionHistory,
                                           Map<String, String> datasourceNameMap,
                                           Map<String, String> schemaNameMap) {
        ExecutionHistoryDTO dto = new ExecutionHistoryDTO();
        BeanUtils.copyProperties(executionHistory, dto);
        dto.setLogType(StrUtil.isBlank(executionHistory.getQueryId()) ? "QUERY_SERVICE" : "INTEGRATION_SERVICE");

        // 构建dbSchemaName字段：dataSourceName/schemaId
        String dataSourceName = datasourceNameMap.get(executionHistory.getDatasourceId());
        String schemaName = schemaNameMap.get(executionHistory.getSchemaId());

        if (StringUtils.isNotBlank(dataSourceName) && StringUtils.isNotBlank(schemaName)) {
            dto.setDbSchemaName(dataSourceName + "/" + schemaName);
        } else if (StringUtils.isNotBlank(dataSourceName)) {
            dto.setDbSchemaName(dataSourceName);
        } else if (StringUtils.isNotBlank(schemaName)) {
            dto.setDbSchemaName(schemaName);
        }
        return dto;
    }

}
