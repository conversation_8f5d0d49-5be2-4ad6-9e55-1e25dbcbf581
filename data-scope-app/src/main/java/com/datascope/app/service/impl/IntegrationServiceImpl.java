package com.datascope.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.datascope.app.common.response.PageResponse;
import com.datascope.app.constants.Constant;
import com.datascope.app.dto.integration.*;
import com.datascope.app.entity.Integration;
import com.datascope.app.exception.BusinessException;
import com.datascope.app.mapper.IntegrationMapper;
import com.datascope.app.model.vo.IntegrationSourceVo;
import com.datascope.app.service.IntegrationService;
import com.datascope.app.util.AuthUtils;
import com.datascope.facade.dto.EnumDTO;
import com.datascope.facade.dto.ParamSourceDTO;
import com.datascope.facade.dto.RequestParamDTO;
import com.datascope.facade.dto.ResponseParamDTO;
import com.datascope.facade.eunm.ReferenceTypeEnum;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 集成管理服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IntegrationServiceImpl extends ServiceImpl<IntegrationMapper, Integration> implements IntegrationService {

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public PageResponse<IntegrationDTO> getIntegrationList(IntegrationQueryParam param) {
        // 构建查询条件
        LambdaQueryWrapper<Integration> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(param.getName())) {
            wrapper.like(Integration::getName, param.getName());
        }
        if (StringUtils.hasText(param.getType())) {
            wrapper.eq(Integration::getType, param.getType());
        }
        if (StringUtils.hasText(param.getStatus())) {
            wrapper.eq(Integration::getStatus, param.getStatus());
        }

        // 排序
        wrapper.orderByDesc(Integration::getUpdatedAt);

        // 分页查询
        Page<Integration> page = new Page<>(param.getPage(), param.getSize());
        IPage<Integration> integrationPage = page(page, wrapper);

        // 转换为DTO
        List<IntegrationDTO> integrationDTOList = integrationPage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

        return PageResponse.of(
                integrationDTOList,
                param.getPage(),
                param.getSize(),
                integrationPage.getTotal()
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IntegrationDTO createIntegration(CreateIntegrationRequest request) {
        Integration integration = new Integration();
        BeanUtils.copyProperties(request, integration);

        // 检查是否已提供ID，否则生成新ID
        if (!StringUtils.hasText(integration.getId())) {
            integration.setId(StringUtils.hasText(request.getId()) ?
                request.getId() : "int-" + UUID.randomUUID().toString().replace("-", ""));
        }

        // 设置初始状态
        integration.setStatus(StringUtils.hasText(request.getStatus()) ?
            request.getStatus() : "DRAFT");

        integration.setCreatedAt(LocalDateTime.now());
        integration.setUpdatedAt(LocalDateTime.now());

        try {
            // 处理JSON字段
            if (request.getQueryParams() != null) {
                integration.setQueryParams(objectMapper.writeValueAsString(request.getQueryParams()));
            }

            if (request.getTableConfig() != null) {
                integration.setTableConfig(objectMapper.writeValueAsString(request.getTableConfig()));
            }

            if (request.getChartConfig() != null) {
                integration.setChartConfig(objectMapper.writeValueAsString(request.getChartConfig()));
            }

            if (Objects.nonNull(request.getRequiredConfig())) {
                integration.setRequiredConfig(objectMapper.writeValueAsString(request.getRequiredConfig()));
            }

            if (request.getMeta() != null) {
                integration.setMeta(objectMapper.writeValueAsString(request.getMeta()));
            }
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常", e);
            throw new RuntimeException("处理JSON数据时发生错误");
        }

        // 添加当前用户信息
        integration.setCreatedBy(AuthUtils.getLoginName());
        integration.setUpdatedBy(integration.getCreatedBy());

        save(integration);

        return convertToDTO(integration);
    }

    @Override
    public IntegrationDTO getIntegrationById(String id) {
        Integration integration = getById(id);
        if (integration == null) {
            return null;
        }
        return convertToDTO(integration);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateIntegrationUrl(String integrationId, String url) {
        Assert.isTrue(StrUtil.isNotBlank(integrationId) && StrUtil.isNotBlank(url), "集成ID和URL不能为空");
        Integration integration = getById(integrationId);
        Assert.isTrue(Objects.nonNull(integration), () -> new RuntimeException("集成不存在: " + integrationId));
        if (url.equals(integration.getIntegrationUrl())) {
            log.info("updateIntegration url: {}, not change", integrationId);
            return;
        }
        LambdaUpdateWrapper<Integration> wrapperUpdate = new LambdaUpdateWrapper<>();
        wrapperUpdate.eq(Integration::getId, integrationId);
        wrapperUpdate.set(Integration::getIntegrationUrl, url);
        this.baseMapper.update(null, wrapperUpdate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IntegrationDTO updateIntegration(UpdateIntegrationRequest request) {
        String id = request.getId();
        Integration integration = getById(id);
        if (integration == null) {
            return null;
        }

        // 更新基本信息
        if (StringUtils.hasText(request.getName())) {
            integration.setName(request.getName());
        }
        if (request.getDescription() != null) {
            integration.setDescription(request.getDescription());
        }
        if (StringUtils.hasText(request.getType())) {
            integration.setType(request.getType());
        }
        if (StringUtils.hasText(request.getStatus())) {
            integration.setStatus(request.getStatus());
        }
        if (StringUtils.hasText(request.getQueryId())) {
            integration.setQueryId(request.getQueryId());
        }
        if (StringUtils.hasText(request.getDataSourceId())) {
            integration.setDataSourceId(request.getDataSourceId());
        }
        if (Objects.nonNull(request.getEnabledCache())) {
            integration.setEnabledCache(request.getEnabledCache());
        }

        try {
            // 处理JSON字段
            if (request.getQueryParams() != null) {
                integration.setQueryParams(objectMapper.writeValueAsString(request.getQueryParams()));
            }

            if (request.getTableConfig() != null) {
                integration.setTableConfig(objectMapper.writeValueAsString(request.getTableConfig()));
            }

            if (request.getChartConfig() != null) {
                integration.setChartConfig(objectMapper.writeValueAsString(request.getChartConfig()));
            }

            if (request.getMeta() != null) {
                integration.setMeta(objectMapper.writeValueAsString(request.getMeta()));
            }

            if (request.getRequiredConfig() != null) {
                integration.setRequiredConfig(objectMapper.writeValueAsString(request.getRequiredConfig()));
            }
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常", e);
            throw new RuntimeException("处理JSON数据时发生错误");
        }

        integration.setUpdatedAt(LocalDateTime.now());
        integration.setUpdatedBy(AuthUtils.getLoginName());

        updateById(integration);

        return convertToDTO(integration);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteIntegration(String id) {
        return removeById(id);
    }

    @Override
    public IntegrationQueryResultDTO previewIntegration(String id) {
        // 这里应该调用查询执行服务，执行集成关联的查询
        // 由于这是一个示例，我们返回一些模拟数据
        IntegrationQueryResultDTO result = new IntegrationQueryResultDTO();

        List<IntegrationQueryResultDTO.ColumnDefinition> columns = new ArrayList<>();
        columns.add(new IntegrationQueryResultDTO.ColumnDefinition()
                .setField("id")
                .setLabel("ID")
                .setType("string"));
        columns.add(new IntegrationQueryResultDTO.ColumnDefinition()
                .setField("name")
                .setLabel("名称")
                .setType("string"));

        List<Map<String, Object>> rows = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("id", "1");
        row1.put("name", "测试数据1");
        rows.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("id", "2");
        row2.put("name", "测试数据2");
        rows.add(row2);

        result.setColumns(columns);
        result.setRows(rows);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IntegrationDTO updateIntegrationStatus(String id, UpdateIntegrationStatusRequest request) {
        Integration integration = getById(id);
        if (integration == null) {
            return null;
        }

        integration.setStatus(request.getStatus());
        integration.setUpdatedAt(LocalDateTime.now());
        integration.setUpdatedBy(AuthUtils.getLoginName()); // 实际应用中应该从安全上下文获取当前用户

        updateById(integration);

        return convertToDTO(integration);
    }

    @Override
    public IntegrationQueryResultDTO executeIntegrationQuery(ExecuteIntegrationQueryRequest request) {
        // 这里应该调用查询执行服务，执行集成关联的查询
        // 由于这是一个示例，我们返回一些模拟数据
        IntegrationQueryResultDTO result = new IntegrationQueryResultDTO();

        List<IntegrationQueryResultDTO.ColumnDefinition> columns = new ArrayList<>();
        columns.add(new IntegrationQueryResultDTO.ColumnDefinition()
                .setField("id")
                .setLabel("ID")
                .setType("string"));
        columns.add(new IntegrationQueryResultDTO.ColumnDefinition()
                .setField("name")
                .setLabel("名称")
                .setType("string"));

        List<Map<String, Object>> rows = new ArrayList<>();
        Map<String, Object> row1 = new HashMap<>();
        row1.put("id", "1");
        row1.put("name", "测试数据1");
        rows.add(row1);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("id", "2");
        row2.put("name", "测试数据2");
        rows.add(row2);

        result.setColumns(columns);
        result.setRows(rows);

        return result;
    }

    /**
     * 将实体转换为DTO
     *
     * @param integration 集成实体
     * @return 集成DTO
     */
    private IntegrationDTO convertToDTO(Integration integration) {
        if (integration == null) {
            return null;
        }

        IntegrationDTO dto = new IntegrationDTO();
        BeanUtils.copyProperties(integration, dto);

        // 处理日期格式
        if (integration.getCreatedAt() != null) {
            dto.setCreatedAt(Date.from(integration.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant()));
        }
        if (integration.getUpdatedAt() != null) {
            dto.setUpdatedAt(Date.from(integration.getUpdatedAt().atZone(ZoneId.systemDefault()).toInstant()));
        }

        try {
            // 处理JSON字段
            if (StringUtils.hasText(integration.getQueryParams())) {
                dto.setQueryParams(objectMapper.readValue(integration.getQueryParams(),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, CreateIntegrationRequest.QueryParam.class)));
            }

            if (StringUtils.hasText(integration.getTableConfig())) {
                dto.setTableConfig(objectMapper.readValue(integration.getTableConfig(), Object.class));
            }

            if (StringUtils.hasText(integration.getChartConfig())) {
                dto.setChartConfig(objectMapper.readValue(integration.getChartConfig(), Object.class));
            }

            if (StringUtils.hasText(integration.getMeta())) {
                dto.setMeta(objectMapper.readValue(integration.getMeta(), Object.class));
            }

            if (StringUtils.hasText(integration.getRequiredConfig())) {
                dto.setRequiredConfig(objectMapper.readValue(integration.getRequiredConfig(), Object.class));
            }
        } catch (JsonProcessingException e) {
            log.error("JSON解析异常", e);
            // 继续处理其他字段，不中断转换
        }

        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void callbackIntegrationConfig(List<IntegrationCallBackDTO> integrationCallBackDTOList) {
        log.info("callbackIntegrationConfig param: {}", JSONUtil.toJsonStr(integrationCallBackDTOList));
        integrationCallBackDTOList.forEach(integrationCallBackDTO -> {
            Integration integration = getById(integrationCallBackDTO.getIntegrationId());
            Assert.isTrue(integration != null, () -> new BusinessException("集成查询不存在: "
                + integrationCallBackDTO.getIntegrationId()));
             ReferenceTypeEnum typeEnum = ReferenceTypeEnum.getByValue(integrationCallBackDTO.getType());
             Assert.isTrue(typeEnum != null, () -> new BusinessException("引用类型不存在: "
                 + integrationCallBackDTO.getType()));
            assert integration != null;
            if (StrUtil.isBlank(integration.getReferenceType())) {
                List<String> referenceTypeList = new ArrayList<>();
                referenceTypeList.add(integrationCallBackDTO.getType());
                integration.setReferenceType(JSONUtil.toJsonStr(referenceTypeList));
            } else {
                List<String> referenceTypeList = JSONUtil.toList(integration.getReferenceType(), String.class);
                if (!referenceTypeList.contains(integrationCallBackDTO.getType())) {
                    referenceTypeList.add(integrationCallBackDTO.getType());
                }
                integration.setReferenceType(JSONUtil.toJsonStr(referenceTypeList));
            }
            updateById(integration);
        });
    }


    @Override
    public List<IntegrationSourceVo> usableQueryGet(DataSourceQueryDTO dataSourceQueryDTO) {
        if (Objects.isNull(dataSourceQueryDTO)) {
            return Collections.emptyList();
        }
        log.info("usableQueryGet param datasourceId: {}, loginName: {}",
            dataSourceQueryDTO.getDatasourceId(), dataSourceQueryDTO.getLoginName());
        Assert.isTrue(StrUtil.isNotBlank(dataSourceQueryDTO.getDatasourceId()), () -> new BusinessException("数据源ID不能为空"));

        LambdaQueryWrapper<Integration> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Integration::getDataSourceId, dataSourceQueryDTO.getDatasourceId());
        wrapper.eq(Integration::getStatus, "ACTIVE");
        if (StrUtil.isNotBlank(dataSourceQueryDTO.getLoginName())) {
            wrapper.eq(Integration::getCreatedBy, dataSourceQueryDTO.getLoginName());
        }
        List<Integration> integrations = this.baseMapper.selectList(wrapper);
        if (CollUtil.isEmpty(integrations)) {
            return Collections.emptyList();
        }

        return integrations.stream().map(integration -> {
            IntegrationSourceVo integrationSourceVo = new IntegrationSourceVo();
            integrationSourceVo.setId(integration.getId());
            integrationSourceVo.setName(integration.getName());
            return integrationSourceVo;
        }).collect(Collectors.toList());
    }

    @Override
    public ParamSourceDTO paramGet(String id) {
        Integration integration = this.baseMapper.selectById(id);
        Assert.isTrue(integration != null, () -> new BusinessException("集成查询不存在: " + id));

        assert integration != null;
        String queryParams = integration.getQueryParams();
        Assert.isTrue(StrUtil.isNotBlank(queryParams), () -> new BusinessException("集成查询参数不存在: " + id));
        String tableConfig = integration.getTableConfig();
        Assert.isTrue(StrUtil.isNotBlank(tableConfig), () -> new BusinessException("集成查询响应参数不存在: " + id));

        ParamSourceDTO paramSourceVo = new ParamSourceDTO();
        try {
            List<RequestParamDTO> requestParamVos = Lists.newArrayList();
            JsonNode jsonNode = objectMapper.readTree(queryParams);
            if (Objects.nonNull(jsonNode) && !jsonNode.isEmpty() && jsonNode.isArray()) {
                jsonNode.forEach(
                    node -> {
                        RequestParamDTO requestParamVo = new RequestParamDTO();
                        requestParamVo.setName(node.get("name").asText());
                        requestParamVo.setUnderName(toCamelCase(requestParamVo.getName()));
                        requestParamVo.setFormType(node.get("formType").asText());
                        requestParamVo.setDescription(node.get("description").asText());
                        if (Objects.nonNull(node.get("required"))) {
                            requestParamVo.setRequired(node.get("required").asBoolean());
                        }
                        if ("select".equalsIgnoreCase(requestParamVo.getFormType())) {
                            List<EnumDTO> options = Lists.newArrayList();
                            JsonNode optionsNode = node.get("options");
                            if (Objects.nonNull(optionsNode) && !optionsNode.isEmpty() && optionsNode.isArray()) {
                                optionsNode.forEach(optionNode -> {
                                    EnumDTO option = new EnumDTO();
                                    option.setLabel(optionNode.get("label").asText());
                                    option.setValue(optionNode.get("value").asText());
                                    options.add(option);
                                });
                            }
                            requestParamVo.setEnums(options);
                        }
                        requestParamVos.add(requestParamVo);
                    }
                );
            }
            paramSourceVo.setRequestParams(requestParamVos);

            List<ResponseParamDTO> responseParamVos = Lists.newArrayList();
            JsonNode node = objectMapper.readTree(tableConfig);
            if (Objects.nonNull(node) && !node.isEmpty() && node.has(Constant.COLUMNS)) {
                JsonNode jsonArray = node.get(Constant.COLUMNS);
                if (Objects.nonNull(jsonArray) && !jsonArray.isEmpty() && jsonArray.isArray()) {
                    jsonArray.forEach(array -> {
                        ResponseParamDTO responseParamVo = new ResponseParamDTO();
                        responseParamVo.setName(array.get("field").asText());
                        responseParamVo.setDescription(array.get("label").asText());
                        responseParamVos.add(responseParamVo);
                    });
                }
            }
            paramSourceVo.setResponseParams(responseParamVos);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return paramSourceVo;
    }

    @Override
    public Boolean paramCheck(String id, UpdateIntegrationRequest request) {
        Integration integration = getById(id);
        if (integration == null) {
            throw new BusinessException("集成不存在: " + id);
        }
        try {
            if (StrUtil.isBlank(integration.getReferenceType())) {
               return false;
            }

            List<String> types = objectMapper.readerForListOf(String.class).readValue(integration.getReferenceType());
            if (!types.contains(ReferenceTypeEnum.YOP.getValue())) {
                return false;
            }

            String newQueryParams = null;
            String newTableConfig = null;
            if (request.getQueryParams() != null) {
                newQueryParams = objectMapper.writeValueAsString(request.getQueryParams());
            }

            if (request.getTableConfig() != null) {
                newTableConfig = objectMapper.writeValueAsString(request.getTableConfig());
            }

            assert newQueryParams != null;
            assert newTableConfig != null;
            return !newQueryParams.equals(integration.getQueryParams())
                || !newTableConfig.equals(integration.getTableConfig())
                || !request.getDataSourceId().equalsIgnoreCase(integration.getDataSourceId())
                || !request.getQueryId().equalsIgnoreCase(integration.getQueryId());
        } catch (JsonProcessingException e) {
            log.error("JSON转换异常", e);
            throw new RuntimeException("处理JSON数据时发生错误");
        }
    }

    private String toCamelCase(String fieldName) {
        if (org.apache.commons.lang3.StringUtils.isBlank(fieldName)) {
            return fieldName;
        }

        // 处理下划线分隔的字段名
        String[] parts = fieldName.split("_");
        if (parts.length == 1) {
            // 单个部分，直接转换为小写
            return fieldName.toLowerCase();
        }

        StringBuilder result = new StringBuilder(parts[0].toLowerCase());
        for (int i = 1; i < parts.length; i++) {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(parts[i])) {
                String part = parts[i].toLowerCase();
                result.append(Character.toUpperCase(part.charAt(0)))
                    .append(part.substring(1));
            }
        }

        return result.toString();
    }

    @Override
    public Map<String, Object> generateSwaggerJson(String id) {
        // 获取集成参数信息
        ParamSourceDTO paramSourceVo = paramGet(id);
        List<RequestParamDTO> requestParams = paramSourceVo.getRequestParams();

        // 构建动态的QueryParamDTO属性
        Map<String, Object> queryParamProperties = new LinkedHashMap<>();
        queryParamProperties.put("page", createIntegerProperty("页码，默认1"));
        queryParamProperties.put("size", createIntegerProperty("每页大小，默认20"));
        // 根据requestParams动态添加属性
        List<String> requireds = Lists.newArrayList();
        if (CollUtil.isNotEmpty(requestParams)) {
            for (RequestParamDTO param : requestParams) {
                String paramName = param.getName();
                String description = param.getDescription();
                String formType = param.getFormType();
                Boolean required = param.getRequired();
                List<EnumDTO> enums = param.getEnums();

                // 创建参数属性
                Map<String, Object> paramProperty = createStringProperty(description);
//                paramProperty.put("required", Lists.newArrayList(required != null ? required : false));
//                paramProperty.put("fieldType", formType);
                if (required != null && required) {
                    requireds.add(paramName);
                }

                // 如果有枚举值，添加enum字段
                if (CollUtil.isNotEmpty(enums)) {
                    List<String> enumValues = new ArrayList<>();
                    List<String> enumNames = new ArrayList<>();
                    for (EnumDTO enumVo : enums) {
                        enumValues.add(enumVo.getValue());
                        enumNames.add(enumVo.getLabel());
                    }
                    paramProperty.put("enum", enumValues);
                    paramProperty.put("x-enum-descriptions", enumNames);
                }

                description = StrUtil.isBlank(description) ? "" : description;
                if (StrUtil.isNotBlank(formType)) {
                    if ("date".equals(formType)) {
                        description = description + "，格式：yyyy-MM-dd";
                    }
                    if ("date-range".equals(formType)) {
                        description = description + "，格式：yyyy-MM-dd,yyyy-MM-dd" + "\n" + "例如：2023-01-01,2023-01-31";
                    }
                    if ("number_range".equals(formType)) {
                        description = description + "，格式：1,3";
                    }
                    if ("select".equals(formType)) {
                        description = description + "，传入枚举key, 映射: " + enums.stream()
                            .map(enumVo -> enumVo.getValue() + "->" + enumVo.getLabel())
                            .collect(Collectors.joining(", "));
                    }
                }
                paramProperty.put("description", description);
                queryParamProperties.put(paramName, paramProperty);
            }
        }

        // 构建完整的Swagger JSON
        Map<String, Object> swaggerJson = new LinkedHashMap<>();
        swaggerJson.put("swagger", "2.0");

        String code = RandomUtil.randomStringUpper( 2);
        // 构建paths
        Map<String, Object> paths = new LinkedHashMap<>();
        Map<String, Object> pathItem = new LinkedHashMap<>();
        Map<String, Object> postOperation = new LinkedHashMap<>();

        postOperation.put("tags", Arrays.asList("IntegrationQueryFacade"));
        postOperation.put("description", "com.datascope.facade.response.ResultResponse query(com.datascope.facade.dto." + "Query" + code +"ParamDTO body)");
        postOperation.put("operationId", "query");

        // 构建parameters
        List<Map<String, Object>> parameters = new ArrayList<>();
        Map<String, Object> parameter = new LinkedHashMap<>();
        parameter.put("in", "formData");
        parameter.put("name", "body");
        parameter.put("required", false);

        Map<String, Object> schema = new LinkedHashMap<>();
        schema.put("$ref", "#/definitions/com.datascope.facade.dto." + "Query" + code +"ParamDTO");
        parameter.put("schema", schema);
        parameters.add(parameter);

        postOperation.put("parameters", parameters);

        // 构建responses
        Map<String, Object> responses = new LinkedHashMap<>();
        Map<String, Object> response200 = new LinkedHashMap<>();
        response200.put("description", "");
        Map<String, Object> responseSchema = new LinkedHashMap<>();
        responseSchema.put("$ref", "#/definitions/com.datascope.facade.response.ResultResponse");
        response200.put("schema", responseSchema);
        response200.put("headers", new LinkedHashMap<>());
        responses.put("200", response200);

        postOperation.put("responses", responses);
        pathItem.put("post", postOperation);

        String pathKey = "/com.datascope.facade.IntegrationQueryFacade/query/-1083384003";
        paths.put(pathKey, pathItem);
        swaggerJson.put("paths", paths);

        // 构建definitions
        Map<String, Object> definitions = new LinkedHashMap<>();

        // QueryParamDTO定义 - 带包名版本
        Map<String, Object> queryParamDTO = new LinkedHashMap<>();
        queryParamDTO.put("type", "object");
        queryParamDTO.put("required", requireds);
        queryParamDTO.put("properties", queryParamProperties);
        definitions.put("com.datascope.facade.dto." + "Query" + code +"ParamDTO", queryParamDTO);

        // QueryParamDTO定义 - 不带包名版本
        Map<String, Object> queryParamDTOSimple = new LinkedHashMap<>();
        queryParamDTOSimple.put("type", "object");
        queryParamDTOSimple.put("properties", queryParamProperties);
        queryParamDTOSimple.put("required", requireds);
        definitions.put("Query" + code +"ParamDTO", queryParamDTOSimple);

        // ResultResponse定义 - 带包名版本
        Map<String, Object> resultResponse = new LinkedHashMap<>();
        resultResponse.put("type", "object");
        Map<String, Object> resultProperties = new LinkedHashMap<>();
        resultProperties.put("success", createBooleanProperty("是否成功"));
        resultProperties.put("code", createStringProperty("响应码"));
        resultProperties.put("message", createStringProperty("响应消息"));
        resultProperties.put("data", createArrayProperty("查询结果数据"));
        resultResponse.put("properties", resultProperties);
        definitions.put("com.datascope.facade.response.ResultResponse", resultResponse);

        // ResultResponse定义 - 不带包名版本
        Map<String, Object> resultResponseSimple = new LinkedHashMap<>();
        resultResponseSimple.put("type", "object");
        resultResponseSimple.put("properties", resultProperties);
        definitions.put("ResultResponse", resultResponseSimple);

//        // ResultResponseMapStringObject定义
//        Map<String, Object> resultResponseMap = new LinkedHashMap<>();
//        resultResponseMap.put("type", "object");
//        Map<String, Object> resultMapProperties = new LinkedHashMap<>();
//        resultMapProperties.put("success", createBooleanProperty("是否成功"));
//        resultMapProperties.put("code", createStringProperty("响应码"));
//        resultMapProperties.put("message", createStringProperty("响应消息"));
//        resultMapProperties.put("data", createArrayPropertyWithObjectItems("查询结果数据"));
//        resultResponseMap.put("properties", resultMapProperties);
//        definitions.put("ResultResponseMapStringObject", resultResponseMap);

        swaggerJson.put("definitions", definitions);

        return swaggerJson;
    }

    private Map<String, Object> createStringProperty(String description) {
        Map<String, Object> property = new LinkedHashMap<>();
        property.put("type", "string");
        property.put("description", description);
        return property;
    }

    private Map<String, Object> createIntegerProperty(String description) {
        Map<String, Object> property = new LinkedHashMap<>();
        property.put("type", "integer");
        property.put("format", "int32");
        property.put("description", description);
        return property;
    }

    private Map<String, Object> createBooleanProperty(String description) {
        Map<String, Object> property = new LinkedHashMap<>();
        property.put("type", "boolean");
        property.put("description", description);
        return property;
    }

    private Map<String, Object> createObjectProperty(String description) {
        Map<String, Object> property = new LinkedHashMap<>();
        property.put("type", "object");
        property.put("description", description);
        return property;
    }

    private Map<String, Object> createArrayProperty(String description) {
        Map<String, Object> property = new LinkedHashMap<>();
        property.put("type", "array");
        property.put("description", description);
        Map<String, Object> items = new LinkedHashMap<>();
        items.put("type", "object");
        property.put("items", items);
        return property;
    }

    private Map<String, Object> createArrayPropertyWithObjectItems(String description) {
        Map<String, Object> property = new LinkedHashMap<>();
        property.put("type", "array");
        property.put("description", description);
        Map<String, Object> items = new LinkedHashMap<>();
        items.put("type", "object");
        Map<String, Object> additionalProperties = new LinkedHashMap<>();
        additionalProperties.put("type", "object");
        items.put("additionalProperties", additionalProperties);
        property.put("items", items);
        return property;
    }
}
