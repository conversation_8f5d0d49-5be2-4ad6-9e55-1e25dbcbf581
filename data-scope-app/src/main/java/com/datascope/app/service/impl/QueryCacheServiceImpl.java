package com.datascope.app.service.impl;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.datascope.app.dto.integration.IntegrationQueryResultDTO;
import com.datascope.app.service.QueryCacheService;
import com.datascope.app.util.SqlCacheKeyGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 查询缓存服务实现类
 * 支持内存缓存和Redis缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class QueryCacheServiceImpl implements QueryCacheService {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public List<Map<String, Object>> getCachedResult(String cacheKey) {
        try {
            // 优先从Redis获取
            if (redisTemplate != null) {
                String cached = redisTemplate.opsForValue().get(cacheKey);
                if (StrUtil.isNotBlank(cached)) {
                    log.info("从Redis获取缓存: {}", cacheKey);
                    return JSONUtil.toBean(cached,
                        new TypeReference<List<Map<String, Object>>>() {
                        }, false);
                }
            }
            return null;
        } catch (Exception e) {
            log.warn("获取缓存失败: {}", cacheKey, e);
            return null;
        }
    }

    @Override
    public CompletableFuture<List<Map<String, Object>>> getCachedResultAsync(String cacheKey) {
        return CompletableFuture.supplyAsync(() -> getCachedResult(cacheKey));
    }

    @Override
    public void cacheResult(String cacheKey, List<Map<String, Object>> result, int expireSeconds) {
        try {
            // 存储到Redis
            if (redisTemplate != null) {
                redisTemplate.opsForValue().set(cacheKey, JSONUtil.toJsonStr(result), expireSeconds, TimeUnit.SECONDS);
                log.info("缓存结果到Redis: {}, expire={}s", cacheKey, expireSeconds);
            }
        } catch (Exception e) {
            log.warn("缓存结果失败: {}", cacheKey, e);
        }
    }

    @Override
    public CompletableFuture<Void> cacheResultAsync(String cacheKey, List<Map<String, Object>> result, int expireSeconds) {
        return CompletableFuture.runAsync(() -> cacheResult(cacheKey, result, expireSeconds));
    }

    @Override
    public String generateCacheKey(String integrationId, Map<String, Object> parameters) {
        StringBuilder sb = new StringBuilder();
        sb.append("integration_query:").append(integrationId);

        if (parameters != null && !parameters.isEmpty()) {
            // 对参数进行排序，确保相同参数生成相同的缓存键
            parameters.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    sb.append(":").append(entry.getKey()).append("=");
                    if (entry.getValue() != null) {
                        sb.append(entry.getValue().toString().hashCode());
                    }
                });
        }

        return sb.toString();
    }

    @Override
    public String generateCacheKey(String sql) {
        return SqlCacheKeyGenerator.generateCacheKey(sql);
    }

    @Override
    public void evictCache(String cacheKey) {
        try {
            // 从Redis删除
            if (redisTemplate != null) {
                redisTemplate.delete(cacheKey);
            }
            log.debug("删除缓存: {}", cacheKey);
        } catch (Exception e) {
            log.warn("删除缓存失败: {}", cacheKey, e);
        }
    }

    @Override
    public void evictCacheBatch(List<String> cacheKeys) {
        if (cacheKeys == null || cacheKeys.isEmpty()) {
            return;
        }

        try {
            // 从Redis批量删除
            if (redisTemplate != null) {
                redisTemplate.delete(cacheKeys);
            }
            log.debug("批量删除缓存: {} 个", cacheKeys.size());
        } catch (Exception e) {
            log.warn("批量删除缓存失败", e);
        }
    }

    @Override
    public void clearAllCache() {
        try {
            // 清理Redis缓存（需要根据实际情况实现）
            if (redisTemplate != null) {
                // 这里需要根据实际的key pattern来清理
                log.info("清理Redis缓存");
            }

            log.info("清理内存缓存完成");
        } catch (Exception e) {
            log.warn("清理缓存失败", e);
        }
    }
}
