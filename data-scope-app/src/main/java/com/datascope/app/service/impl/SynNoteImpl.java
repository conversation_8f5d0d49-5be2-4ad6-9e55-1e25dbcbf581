package com.datascope.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.datascope.app.common.config.DynamicDataSourcePoolManager;
import com.datascope.app.entity.Datasource;
import com.datascope.app.entity.Query;
import com.datascope.app.entity.QueryVersion;
import com.datascope.app.entity.Schema;
import com.datascope.app.mapper.DatasourceMapper;
import com.datascope.app.mapper.QueryMapper;
import com.datascope.app.mapper.QueryVersionMapper;
import com.datascope.app.mapper.SchemaMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *
 * 同步笔记实现类
 */
@Service
@Slf4j
public class SynNoteImpl {

    @Autowired
    private DatasourceMapper datasourceMapper;

    @Autowired
    private DynamicDataSourcePoolManager poolManager;

    @Autowired
    private QueryMapper queryMapper;

    @Autowired
    private QueryVersionMapper queryVersionMapper;

    @Autowired
    private SchemaMapper schemaMapper;
    private DataSource dataSource;

    @Transactional(rollbackFor = Exception.class)
    public void syncNote(List<String> names) {
        if (CollUtil.isEmpty(names)) {
            return;
        }
        String currentUser = names.get(0);

        LambdaQueryWrapper<Datasource> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Datasource::getName, "boss");
        List<Datasource> datasources = datasourceMapper.selectList(lambdaQueryWrapper);
        if (CollUtil.isEmpty(datasources)) {
            return;
        }
        Datasource dataSourceStart = datasources.get(0);
        // search source
        dataSourceStart.setSchema("DATABANK");
        JdbcTemplate jdbcTemplate = poolManager.getJdbcTemplate(dataSourceStart, "DATABANK");
        names = names.stream().map(name -> "'" + name + "'").collect(Collectors.toList());
        log.info("search note: names: {}", names);
        List<Map<String, Object>> rows = jdbcTemplate
        .queryForList("select NOTE_NAME,  NOTE_QUERY, NOTE_SCHEMA , tu.LOGIN_NAME, td.DS_NAME,td.DB_TYPE,td.DS_CONN_STR\n" +
            "from TBL_DB_USER_NOTE tn left join TBL_DB_USER tu on tn.USER_ID = tu.USER_ID \n" +
            "LEFT JOIN TBL_DB_NOTE te on tn.NOTE_ID = te.NOTE_ID\n" +
            "LEFT JOIN TBL_DB_DATASOURCE td on td.DS_ID = te.NOTE_DS_ID\n" +
            "where tu.LOGIN_NAME in (" + CollUtil.join(names, ",") + ")");
        if (CollUtil.isEmpty(rows)) {
            return;
        }
        log.info("search note content: {}", rows);

        List<Datasource> allDataSources = this.datasourceMapper.selectList(new LambdaQueryWrapper<>());
        if (CollUtil.isEmpty(allDataSources)) {
            return;
        }

        Map<String, Datasource> urlMap = allDataSources.stream().collect(Collectors.toMap(ds -> {
                String key = ds.getHost() + ":" + ds.getPort();
                String url = null;
                if ("mysql".equals(ds.getType())) {
                    url = "jdbc:mysql://" + key;
                }
                if ("db2".equals(ds.getType())) {
                    url = "jdbc:db2://" + key + "/" + ds.getDatabaseName();
                }
                return url.toLowerCase();
            },
            ds -> ds, (v1, v2) -> v1));

        Map<String, Datasource> dbMap = allDataSources.stream().collect(Collectors.toMap(ds -> {
                if ("mysql".equalsIgnoreCase(ds.getType())) {
                    return ds.getDatabaseName();
                }
                if ("db2".equalsIgnoreCase(ds.getType())) {
                    return ds.getSchema();
                }
                return null;
            }, ds -> ds, (v1, v2) -> v1));

        rows.forEach(row -> {
            String noteName = String.valueOf(row.get("NOTE_NAME"));
            String noteQuery = String.valueOf(row.get("NOTE_QUERY"));
            String noteSchema = String.valueOf(row.get("NOTE_SCHEMA"));
            // DB2	MYSQL
            String dbType = String.valueOf(row.get("DB_TYPE"));
            // ******************************************
            // *********************************
            String dsConnStr = String.valueOf(row.get("DS_CONN_STR"));
            if (StrUtil.isBlank(noteName) || StrUtil.isBlank(noteQuery)
                || StrUtil.isBlank(noteSchema) || StrUtil.isBlank(dbType) || StrUtil.isBlank(dsConnStr)) {
                return;
            }
            Datasource datasourceConn = urlMap.get(dsConnStr.toLowerCase());
            Datasource datasourceSchema = dbMap.get(noteSchema);
            Datasource datasource = datasourceConn;
            // db2
            if (Objects.isNull(datasource)) {
                datasource = datasourceSchema;
            }
            if (datasource == null) {
                log.info("找不到数据源: {}", noteSchema);
                return;
            }
            log.info("datasource info: {}", datasource.getHost() + ":" + datasource.getPort()
                + ", db: " + datasource.getDatabaseName() + ", type: " + datasource.getType()
                + ", schema: " +datasource.getSchema());

            LambdaQueryWrapper<Schema> schemaLambdaQueryWrapper = new LambdaQueryWrapper<>();
            schemaLambdaQueryWrapper.eq(Schema::getDatasourceId, datasource.getId());
            schemaLambdaQueryWrapper.eq(Schema::getName, noteSchema);
            Schema schema = schemaMapper.selectOne(schemaLambdaQueryWrapper);
            if (schema == null) {
                log.info("找不到schema: {}", noteSchema);
                return;
            }
            insertNote(currentUser, noteName, noteQuery, schema.getId(), datasource);
        });

    }


    private void insertNote(String currentUser, String noteName,
                            String noteQuery, String schemaId,
                            Datasource datasource) {

        List<Query> queries = queryMapper.selectList(new LambdaQueryWrapper<Query>().eq(Query::getName, noteName));
        if (CollUtil.isNotEmpty(queries)) {
            log.info("查询笔记已存在: {}", noteName);
            return;
        }

        // 生成ID
        String id = UUID.randomUUID().toString().replace("-", "");
        Date now = new Date();

        // 构建Query实体
        Query query = Query.builder()
            .id(id)
            .name(noteName)
            .description("")
            .dataSourceId(datasource.getId())
            // 初始状态为草稿
            .status("DRAFT")
            .serviceStatus("ENABLED")
            .queryType("SQL")
            .isPublic(false)
            .executionCount(0)
            .createdBy(currentUser)
            .createdAt(now)
            .updatedBy(currentUser)
            .updatedAt(now)
            .build();

        // 保存到数据库
        int result = queryMapper.insert(query);
        log.info("查询保存结果: {}, 影响行数: {}", result > 0 ? "成功" : "失败", result);

        if (result <= 0) {
            log.error("创建查询失败");
            throw new RuntimeException("创建查询失败");
        }

        // 创建初始版本
        QueryVersion initialVersion = QueryVersion.builder()
            .id(UUID.randomUUID().toString().replace("-", ""))
            .queryId(id)
            .versionNumber(1)
            .name("初始版本")
            .sqlContent(noteQuery)
            .dataSourceId(datasource.getId())
            .status("DRAFT")
            .isLatest(true)
            .createdBy(currentUser)
            .createdAt(now)
            .updatedBy(currentUser)
            .updatedAt(now)
            .schemaId(schemaId)
            .build();

        int versionResult = queryVersionMapper.insert(initialVersion);
        log.info("查询版本保存结果: {}, 影响行数: {}", versionResult > 0 ? "成功" : "失败", versionResult);
    }
}
