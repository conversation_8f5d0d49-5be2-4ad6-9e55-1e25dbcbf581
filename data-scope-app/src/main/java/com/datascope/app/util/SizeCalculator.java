package com.datascope.app.util;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.ObjectOutputStream;
import java.util.List;
import java.util.Map;

/**
 * 对象大小计算工具类
 * 用于计算对象在内存中的大小
 *
 * <AUTHOR>
 */
@Slf4j
public class SizeCalculator {

    /**
     * 3MB的字节数
     */
    private static final long MAX_CACHE_SIZE = 3 * 1024 * 1024;

    /**
     * 计算List<Map<String, Object>>对象的大小（字节）
     *
     * @param result 查询结果
     * @return 对象大小（字节）
     */
    public static long calculateSize(List<Map<String, Object>> result) {
        if (result == null || result.isEmpty()) {
            return 0;
        }

        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(baos);
            oos.writeObject(result);
            oos.close();
            return baos.size();
        } catch (Exception e) {
            log.warn("计算对象大小失败，使用估算方法", e);
            // 如果序列化失败，使用估算方法
            return estimateSize(result);
        }
    }

    /**
     * 估算List<Map<String, Object>>对象的大小
     * 这是一个粗略的估算方法，用于序列化失败时的降级处理
     *
     * @param result 查询结果
     * @return 估算的对象大小（字节）
     */
    private static long estimateSize(List<Map<String, Object>> result) {
        long totalSize = 0;
        
        // 估算List本身的开销
        totalSize += 16; // ArrayList对象头
        totalSize += result.size() * 8; // 引用数组
        
        for (Map<String, Object> row : result) {
            if (row == null) continue;
            
            // 估算Map的开销
            totalSize += 16; // HashMap对象头
            totalSize += row.size() * 32; // 估算每个entry的开销
            
            for (Map.Entry<String, Object> entry : row.entrySet()) {
                // 估算key的大小
                if (entry.getKey() != null) {
                    totalSize += entry.getKey().length() * 2; // 字符串字符数 * 2字节
                }
                
                // 估算value的大小
                Object value = entry.getValue();
                if (value != null) {
                    totalSize += estimateObjectSize(value);
                }
            }
        }
        
        return totalSize;
    }

    /**
     * 估算单个对象的大小
     *
     * @param obj 对象
     * @return 估算大小（字节）
     */
    private static long estimateObjectSize(Object obj) {
        if (obj == null) {
            return 0;
        }
        
        if (obj instanceof String) {
            return ((String) obj).length() * 2 + 24; // 字符串长度 * 2 + 对象头
        } else if (obj instanceof Number) {
            if (obj instanceof Integer || obj instanceof Long) {
                return 16; // 包装类对象头 + 基本类型
            } else if (obj instanceof Double || obj instanceof Float) {
                return 16;
            }
            return 24; // 其他Number类型
        } else if (obj instanceof Boolean) {
            return 16;
        } else if (obj instanceof java.util.Date) {
            return 24;
        } else {
            // 对于其他类型，使用保守估算
            return 32;
        }
    }

    /**
     * 检查对象大小是否超过缓存限制
     *
     * @param result 查询结果
     * @return true如果大小超过限制，false否则
     */
    public static boolean isSizeExceeded(List<Map<String, Object>> result) {
        long size = calculateSize(result);
        boolean exceeded = size > MAX_CACHE_SIZE;
        
        if (exceeded) {
            log.info("查询结果大小超过缓存限制: {} bytes (限制: {} bytes)", size, MAX_CACHE_SIZE);
        } else {
            log.debug("查询结果大小: {} bytes (限制: {} bytes)", size, MAX_CACHE_SIZE);
        }
        
        return exceeded;
    }

    /**
     * 获取最大缓存大小限制（字节）
     *
     * @return 最大缓存大小
     */
    public static long getMaxCacheSize() {
        return MAX_CACHE_SIZE;
    }
}
