package com.datascope.app.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL缓存key生成器
 * 基于完整SQL语句生成缓存key，处理条件顺序变化的情况
 *
 * <AUTHOR>
 */
@Slf4j
public class SqlCacheKeyGenerator {

    /**
     * WHERE条件正则表达式
     * 支持MySQL/PostgreSQL的LIMIT和DB2的FETCH FIRST ... ROWS ONLY语法
     */
    private static final Pattern WHERE_PATTERN = Pattern.compile(
        "(?i)\\bwhere\\b\\s+(.+?)(?=\\s+(?:group\\s+by|order\\s+by|limit|fetch\\s+first|$)|$)",
        Pattern.DOTALL | Pattern.CASE_INSENSITIVE
    );

    /**
     * 生成SQL缓存key
     *
     * @param appendSql 完整的SQL语句（可直接在SQL客户端执行）
     * @return 缓存key
     */
    public static String generateCacheKey(String appendSql) {
        if (StrUtil.isBlank(appendSql)) {
            return "empty_sql";
        }

        try {
            // 1. 标准化SQL（去除多余空格、换行等）
            String normalizedSql = normalizeSql(appendSql);

            // 2. 分离基础SQL（包含FROM、JOIN）、WHERE条件和排序/分页子句
            String baseSql = extractBaseSql(normalizedSql);
            String whereClause = extractWhereClause(normalizedSql);
            String sortAndLimitClause = extractSortAndLimitClause(normalizedSql);

            // 3. 标准化WHERE条件（处理顺序，保持值敏感）
            String normalizedWhereClause = normalizeWhereClauseKeepValues(whereClause);

            // 4. 标准化排序和分页子句
            String normalizedSortAndLimit = normalizeSortAndLimitClause(sortAndLimitClause);

            // 5. 构建缓存key
            StringBuilder keyBuilder = new StringBuilder();
            keyBuilder.append("sql_cache:");
            keyBuilder.append(hashString(baseSql)).append(":");
            keyBuilder.append(hashString(normalizedWhereClause));
            if (StrUtil.isNotBlank(normalizedSortAndLimit)) {
                keyBuilder.append(":").append(hashString(normalizedSortAndLimit));
            }

            return keyBuilder.toString();

        } catch (Exception e) {
            log.warn("生成SQL缓存key失败，使用fallback策略: {}", e.getMessage());
            return "sql_cache_fallback:" + hashString(appendSql);
        }
    }

    /**
     * 标准化SQL语句
     */
    private static String normalizeSql(String sql) {
        if (StrUtil.isBlank(sql)) {
            return "";
        }

        return sql.trim()
            .replaceAll("\\s+", " ")           // 多个空格替换为单个空格
            .replaceAll("\\s*=\\s*", "=")      // 等号前后空格
            .replaceAll("\\s*<\\s*", "<")      // 小于号前后空格
            .replaceAll("\\s*>\\s*", ">")      // 大于号前后空格
            .replaceAll("\\s*!=\\s*", "!=")    // 不等号前后空格
            .replaceAll("\\s*<=\\s*", "<=")    // 小于等于号前后空格
            .replaceAll("\\s*>=\\s*", ">=")    // 大于等于号前后空格
            .toLowerCase();                     // 转换为小写
    }

    /**
     * 提取基础SQL（包含FROM、JOIN，不包含WHERE条件）
     */
    private static String extractBaseSql(String sql) {
        // 移除WHERE子句及其后面的所有内容，保留FROM和JOIN
        String baseSql = sql.replaceAll("(?i)\\bwhere\\b.*", "").trim();
        // 标准化基础SQL，移除表别名
        return normalizeBaseSql(baseSql);
    }



    /**
     * 标准化基础SQL，移除表别名
     */
    private static String normalizeBaseSql(String baseSql) {
        if (StrUtil.isBlank(baseSql)) {
            return baseSql;
        }

        try {
            // 1. 标准化SELECT部分，移除字段别名
            String normalized = baseSql.replaceAll("(?i)\\bselect\\s+", "select ");

            // 2. 标准化FROM部分，移除表别名
            // 匹配模式：FROM table [alias] 或 FROM table [alias] JOIN table [alias]
            normalized = normalized.replaceAll("(?i)\\bfrom\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s+([a-zA-Z]\\w*)", "from $1");
            normalized = normalized.replaceAll("(?i)\\bjoin\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s+([a-zA-Z]\\w*)", "join $1");

            // 3. 标准化ON条件中的表别名引用
            // 将 u.id = o.user_id 标准化为 id = user_id
            normalized = normalized.replaceAll("([a-zA-Z]\\w*)\\.([a-zA-Z_][a-zA-Z0-9_]*)", "$2");

            return normalized.trim();

        } catch (Exception e) {
            log.warn("标准化基础SQL失败，使用原始SQL: {}", e.getMessage());
            return baseSql;
        }
    }

    /**
     * 提取WHERE子句（包含WHERE条件及其后面的所有子句）
     */
    private static String extractWhereClause(String sql) {
        Matcher matcher = WHERE_PATTERN.matcher(sql);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }
        // 如果没有WHERE子句，返回空字符串
        return "";
    }

        /**
     * 提取排序和分页子句（ORDER BY, GROUP BY, LIMIT, FETCH FIRST等）
     * 支持MySQL/PostgreSQL的LIMIT和DB2的FETCH FIRST ... ROWS ONLY语法
     */
    private static String extractSortAndLimitClause(String sql) {
        // 查找WHERE子句后面的内容
        Matcher whereMatcher = WHERE_PATTERN.matcher(sql);
        if (whereMatcher.find()) {
            String afterWhere = sql.substring(whereMatcher.end());
            if (afterWhere.trim().isEmpty()) {
                return "";
            }

            // 提取ORDER BY, GROUP BY, LIMIT, FETCH FIRST等子句
            StringBuilder clauses = new StringBuilder();

            // 检查是否有GROUP BY
            if (afterWhere.toLowerCase().contains("group by")) {
                int groupByIndex = afterWhere.toLowerCase().indexOf("group by");
                clauses.append(afterWhere.substring(groupByIndex)).append(" ");
            }

            // 检查是否有ORDER BY
            if (afterWhere.toLowerCase().contains("order by")) {
                int orderByIndex = afterWhere.toLowerCase().indexOf("order by");
                clauses.append(afterWhere.substring(orderByIndex)).append(" ");
            }

            // 检查是否有LIMIT (MySQL/PostgreSQL)
            if (afterWhere.toLowerCase().contains("limit")) {
                int limitIndex = afterWhere.toLowerCase().indexOf("limit");
                clauses.append(afterWhere.substring(limitIndex)).append(" ");
            }

            // 检查是否有FETCH FIRST ... ROWS ONLY (DB2)
            if (afterWhere.toLowerCase().contains("fetch first")) {
                int fetchIndex = afterWhere.toLowerCase().indexOf("fetch first");
                clauses.append(afterWhere.substring(fetchIndex)).append(" ");
            }

            return clauses.toString().trim();
        }

        // 如果没有WHERE子句，直接从整个SQL中提取排序和分页子句
        String normalizedSql = sql.toLowerCase();
        StringBuilder clauses = new StringBuilder();

        // 检查是否有GROUP BY
        if (normalizedSql.contains("group by")) {
            int groupByIndex = normalizedSql.indexOf("group by");
            clauses.append(sql.substring(groupByIndex)).append(" ");
        }

        // 检查是否有ORDER BY
        if (normalizedSql.contains("order by")) {
            int orderByIndex = normalizedSql.indexOf("order by");
            clauses.append(sql.substring(orderByIndex)).append(" ");
        }

        // 检查是否有LIMIT (MySQL/PostgreSQL)
        if (normalizedSql.contains("limit")) {
            int limitIndex = normalizedSql.indexOf("limit");
            clauses.append(sql.substring(limitIndex)).append(" ");
        }

        // 检查是否有FETCH FIRST ... ROWS ONLY (DB2)
        if (normalizedSql.contains("fetch first")) {
            int fetchIndex = normalizedSql.indexOf("fetch first");
            clauses.append(sql.substring(fetchIndex)).append(" ");
        }

        return clauses.toString().trim();
    }

    /**
     * 标准化WHERE条件
     * 处理条件顺序变化，确保相同条件生成相同的key
     */
    private static String normalizeWhereClause(String whereClause) {
        if (StrUtil.isBlank(whereClause)) {
            return "";
        }

        try {
            // 1. 分割条件（按AND/OR分割）
            List<String> conditions = splitConditions(whereClause);

            // 2. 标准化每个条件
            List<String> normalizedConditions = new ArrayList<>();
            for (String condition : conditions) {
                String normalized = normalizeCondition(condition);
                if (StrUtil.isNotBlank(normalized)) {
                    normalizedConditions.add(normalized);
                }
            }

            // 3. 排序条件（确保顺序一致性）
            Collections.sort(normalizedConditions);

            // 4. 重新组合
            return String.join(" AND ", normalizedConditions);

        } catch (Exception e) {
            log.warn("标准化WHERE条件失败，使用原始条件: {}", e.getMessage());
            return whereClause;
        }
    }

    /**
     * 标准化WHERE条件，保持值敏感，只处理条件顺序
     * 确保相同逻辑的查询生成相同的key
     */
    private static String normalizeWhereClauseKeepValues(String whereClause) {
        if (StrUtil.isBlank(whereClause)) {
            return "";
        }

        try {
            // 1. 先处理括号，将括号内的内容标记为特殊token
            Map<String, String> tokenMap = new HashMap<>();
            String processedClause = processParenthesesWithTokens(whereClause, tokenMap);

            // 2. 按OR分割条件组
            List<String> orGroups = splitByOperator(processedClause, "\\bor\\b");
            List<String> normalizedGroups = new ArrayList<>();

            for (String orGroup : orGroups) {
                // 3. 按AND分割每个OR组内的条件
                List<String> andConditions = splitByOperator(orGroup, "\\band\\b");
                List<String> normalizedConditions = new ArrayList<>();

                for (String condition : andConditions) {
                    String normalized = normalizeConditionKeepValue(condition.trim());
                    if (StrUtil.isNotBlank(normalized)) {
                        normalizedConditions.add(normalized);
                    }
                }

                // 4. 排序AND条件（确保顺序一致性）
                Collections.sort(normalizedConditions);
                String normalizedGroup = String.join(" AND ", normalizedConditions);
                if (StrUtil.isNotBlank(normalizedGroup)) {
                    normalizedGroups.add(normalizedGroup);
                }
            }

            // 5. 排序OR组（确保顺序一致性）
            Collections.sort(normalizedGroups);
            String result = String.join(" OR ", normalizedGroups);

            // 6. 恢复token
            for (Map.Entry<String, String> entry : tokenMap.entrySet()) {
                result = result.replace(entry.getKey(), entry.getValue());
            }

            return result;

        } catch (Exception e) {
            log.warn("标准化WHERE条件失败，使用原始条件: {}", e.getMessage());
            return whereClause;
        }
    }

    /**
     * 分割WHERE条件
     */
    private static List<String> splitConditions(String whereClause) {
        List<String> conditions = new ArrayList<>();

        // 使用AND分割条件（忽略OR，因为OR通常用于不同的逻辑）
        String[] parts = whereClause.split("(?i)\\band\\b");

        for (String part : parts) {
            String trimmed = part.trim();
            if (StrUtil.isNotBlank(trimmed)) {
                // 处理第一个条件（可能以WHERE开头）
                if (trimmed.toLowerCase().startsWith("where")) {
                    trimmed = trimmed.substring(5).trim();
                }
                if (StrUtil.isNotBlank(trimmed)) {
                    conditions.add(trimmed);
                }
            }
        }

        return conditions;
    }

    /**
     * 标准化单个条件
     */
    private static String normalizeCondition(String condition) {
        if (StrUtil.isBlank(condition)) {
            return "";
        }

        try {
            // 处理常见的条件格式
            // 1. 字段名 操作符 值
            // 2. 字段名 IN (值1, 值2)
            // 3. 字段名 LIKE '值'

            // 去除可能的括号
            String cleanCondition = condition.replaceAll("[()]", "").trim();

            // 分割条件
            String[] parts = cleanCondition.split("\\s+");
            if (parts.length >= 3) {
                String fieldName = parts[0].toLowerCase();
                String operator = parts[1].toLowerCase();
                String value = String.join(" ", Arrays.copyOfRange(parts, 2, parts.length));

                // 标准化操作符
                String normalizedOperator = normalizeOperator(operator);

                // 标准化值
                String normalizedValue = normalizeValue(value);

                return fieldName + normalizedOperator + normalizedValue;
            }

            // 如果无法解析，返回原始条件的hash
            return "condition:" + hashString(condition);

        } catch (Exception e) {
            log.debug("标准化条件失败: {}", condition, e);
            return "condition:" + hashString(condition);
        }
    }

    /**
     * 标准化单个条件，保持值敏感
     */
    private static String normalizeConditionKeepValue(String condition) {
        if (StrUtil.isBlank(condition)) {
            return "";
        }

        try {
            // 去除可能的括号
            String cleanCondition = condition.replaceAll("[()]", "").trim();

            // 分割条件：字段名 操作符 值
            // 先尝试按空格分割，如果没有空格，则按操作符分割
            String[] parts;
            if (cleanCondition.contains(" ")) {
                parts = cleanCondition.split("\\s+");
            } else {
                // 没有空格的情况，按操作符分割
                parts = splitByOperator(cleanCondition);
            }

            if (parts.length >= 3) {
                String fieldName = parts[0].toLowerCase();
                String operator = parts[1].toLowerCase();
                String value = String.join(" ", Arrays.copyOfRange(parts, 2, parts.length));

                // 标准化字段名，移除表别名前缀
                String normalizedFieldName = normalizeFieldName(fieldName);

                // 标准化操作符
                String normalizedOperator = normalizeOperator(operator);

                // 特殊处理IN条件
                if ("in".equals(normalizedOperator)) {
                    value = normalizeInCondition(value);
                }

                // 保持值敏感，不进行值的标准化
                return normalizedFieldName + normalizedOperator + value;
            }

            return "condition:" + hashString(condition);

        } catch (Exception e) {
            return "condition:" + hashString(condition);
        }
    }



    /**
     * 标准化排序和分页子句
     * 支持MySQL/PostgreSQL的LIMIT和DB2的FETCH FIRST ... ROWS ONLY语法
     */
    private static String normalizeSortAndLimitClause(String clause) {
        if (StrUtil.isBlank(clause)) {
            return "";
        }

        try {
            String normalized = clause.toLowerCase().trim();

            // 标准化空格
            normalized = normalized.replaceAll("\\s+", " ");

            // 标准化常见的排序关键字
            normalized = normalized.replaceAll("\\basc\\b", "asc");
            normalized = normalized.replaceAll("\\bdesc\\b", "desc");

            // 标准化LIMIT语法 (MySQL/PostgreSQL)
            normalized = normalized.replaceAll("\\blimit\\s+", "limit ");

            // 标准化FETCH FIRST语法 (DB2)
            normalized = normalized.replaceAll("\\bfetch\\s+first\\s+", "fetch first ");
            normalized = normalized.replaceAll("\\brows\\s+only\\b", "rows only");
            normalized = normalized.replaceAll("\\brow\\s+only\\b", "row only");

            return normalized.trim();

        } catch (Exception e) {
            log.warn("标准化排序和分页子句失败，使用原始子句: {}", e.getMessage());
            return clause;
        }
    }

    /**
     * 标准化字段名，移除表别名前缀
     */
    private static String normalizeFieldName(String fieldName) {
        if (StrUtil.isBlank(fieldName)) {
            return fieldName;
        }

        // 如果字段名包含表别名前缀（如 u.age），则移除前缀
        if (fieldName.contains(".")) {
            String[] parts = fieldName.split("\\.");
            if (parts.length == 2) {
                return parts[1]; // 返回字段名部分，不包含表别名
            }
        }

        return fieldName;
    }

    /**
     * 标准化操作符
     */
    private static String normalizeOperator(String operator) {
        if (operator == null) {
            return "=";
        }

        switch (operator.toLowerCase()) {
            case "=":
            case "==":
                return "=";
            case "<>":
            case "!=":
                return "!=";
            case "<":
                return "<";
            case ">":
                return ">";
            case "<=":
                return "<=";
            case ">=":
                return ">=";
            case "like":
                return "like";
            case "in":
                return "in";
            case "not in":
                return "notin";
            case "is":
                return "is";
            case "is not":
                return "isnot";
            default:
                return operator.toLowerCase();
        }
    }

    /**
     * 标准化值
     */
    private static String normalizeValue(String value) {
        if (StrUtil.isBlank(value)) {
            return "null";
        }

        // 去除引号
        String normalized = value.replaceAll("^['\"]|['\"]$", "");

        // 处理数字
        if (normalized.matches("^-?\\d+(\\.\\d+)?$")) {
            return normalized;
        }

        // 处理布尔值
        if ("true".equalsIgnoreCase(normalized) || "false".equalsIgnoreCase(normalized)) {
            return normalized.toLowerCase();
        }

        // 处理NULL
        if ("null".equalsIgnoreCase(normalized)) {
            return "null";
        }

        // 处理IN子句的值
        if (normalized.contains(",")) {
            String[] values = normalized.split(",");
            List<String> sortedValues = new ArrayList<>();
            for (String val : values) {
                sortedValues.add(normalizeValue(val.trim()));
            }
            Collections.sort(sortedValues);
            return "in:" + String.join(",", sortedValues);
        }

        // 其他值返回hash（避免值过长）
        if (normalized.length() > 50) {
            return "v:" + hashString(normalized.substring(0, 50));
        }
        return normalized;
    }

    /**
     * 标准化参数
     */
    private static String hashParameters(Map<String, Object> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return "no_params";
        }

        try {
            // 对参数进行排序，确保顺序一致性
            List<String> sortedKeys = new ArrayList<>(parameters.keySet());
            Collections.sort(sortedKeys);

            StringBuilder paramBuilder = new StringBuilder();
            for (String key : sortedKeys) {
                Object value = parameters.get(key);
                paramBuilder.append(key).append("=").append(normalizeParameterValue(value)).append("|");
            }

            return hashString(paramBuilder.toString());

        } catch (Exception e) {
            log.warn("标准化参数失败，使用原始参数hash: {}", e.getMessage());
            return hashString(parameters.toString());
        }
    }

    /**
     * 标准化参数值
     */
    private static String normalizeParameterValue(Object value) {
        if (value == null) {
            return "null";
        }

        if (value instanceof Number) {
            return value.toString();
        }

        if (value instanceof Boolean) {
            return value.toString();
        }

        if (value instanceof String) {
            String str = (String) value;
            if (str.length() > 50) {
                // 长字符串只取前50个字符的hash
                return "long:" + hashString(str.substring(0, 50));
            }
            return str;
        }

        // 其他类型返回hash
        return "obj:" + hashString(value.toString());
    }

    /**
     * 计算字符串hash
     */
    private static String hashString(String input) {
        if (StrUtil.isBlank(input)) {
            return "0";
        }

        int hash = input.hashCode();
        return Integer.toHexString(Math.abs(hash));
    }

    /**
     * 测试方法：验证缓存key生成逻辑
     */
    public static void main(String[] args) {
        System.out.println("=== SQL缓存Key生成器测试 ===\n");

        // 测试用例1：基础查询
        String sql1 = "SELECT * FROM users order by id desc limit 20";
        String sql111 = "SELECT * FROM users order by id desc  limit 20";
        String key1 = generateCacheKey(sql1);
        String key111 = generateCacheKey(sql111);
        System.out.println("SQL1: " + sql1);
        System.out.println("Key1: " + key1);
        System.out.println("Key111: " + key111);

        // 测试用例2：条件顺序不同的查询（应该生成相同的key）
        String sql2 = "SELECT * FROM users WHERE age > 18 AND status = 'active'";
        String sql3 = "SELECT * FROM users WHERE status = 'active' AND age > 18";
        String key2 = generateCacheKey(sql2);
        String key3 = generateCacheKey(sql3);
        System.out.println("SQL2: " + sql2);
        System.out.println("Key2: " + key2);
        System.out.println("SQL3: " + sql3);
        System.out.println("Key3: " + key3);
        System.out.println("✅ Key2 == Key3: " + key2.equals(key3));
        System.out.println();

        // 测试用例3：OR条件顺序不同的查询（应该生成相同的key）
        String sql4 = "SELECT * FROM users WHERE age > 18 OR status = 'active'";
        String sql5 = "SELECT * FROM users WHERE status = 'active' OR age > 18";
        String key4 = generateCacheKey(sql4);
        String key5 = generateCacheKey(sql5);
        System.out.println("SQL4: " + sql4);
        System.out.println("Key4: " + key4);
        System.out.println("SQL5: " + sql5);
        System.out.println("Key5: " + key5);
        System.out.println("✅ Key4 == Key5: " + key4.equals(key5));
        System.out.println();

        // 测试用例4：复杂条件顺序不同的查询（应该生成相同的key）
        String sql6 = "SELECT * FROM users WHERE age > 18 AND (status = 'active' OR vip = true)";
        String sql7 = "SELECT * FROM users WHERE (status = 'active' OR vip = true) AND age > 18";
        String key6 = generateCacheKey(sql6);
        String key7 = generateCacheKey(sql7);
        System.out.println("SQL6: " + sql6);
        System.out.println("Key6: " + key6);
        System.out.println("SQL7: " + sql7);
        System.out.println("Key7: " + key7);
        System.out.println("✅ Key6 == Key7: " + key6.equals(key7));
        System.out.println();

        // 测试用例5：值不同的查询（应该生成不同的key）
        String sql8 = "SELECT * FROM users WHERE age > 18";
        String sql9 = "SELECT * FROM users WHERE age > 25";
        String key8 = generateCacheKey(sql8);
        String key9 = generateCacheKey(sql9);
        System.out.println("SQL8: " + sql8);
        System.out.println("Key8: " + key8);
        System.out.println("SQL9: " + sql9);
        System.out.println("Key9: " + key9);
        System.out.println("❌ Key8 == Key9: " + key8.equals(key9) + " (应该不同)");
        System.out.println();

        // 测试用例6：字段不同的查询（应该生成不同的key）
        String sql10 = "SELECT * FROM users WHERE age > 18";
        String sql11 = "SELECT * FROM users WHERE name = '张三'";
        String key10 = generateCacheKey(sql10);
        String key11 = generateCacheKey(sql11);
        System.out.println("SQL10: " + sql10);
        System.out.println("Key10: " + key10);
        System.out.println("SQL11: " + sql11);
        System.out.println("Key11: " + key11);
        System.out.println("❌ Key10 == Key11: " + key10.equals(key11) + " (应该不同)");
        System.out.println();

        // 测试用例7：表名不同的查询（应该生成不同的key）
        String sql12 = "SELECT * FROM users WHERE age > 18";
        String sql13 = "SELECT * FROM orders WHERE age > 18";
        String key12 = generateCacheKey(sql12);
        String key13 = generateCacheKey(sql13);
        System.out.println("SQL12: " + sql12);
        System.out.println("Key12: " + key12);
        System.out.println("SQL13: " + sql13);
        System.out.println("Key13: " + key13);
        System.out.println("❌ Key12 == Key13: " + key12.equals(key13) + " (应该不同)");
        System.out.println();

        // 测试用例8：JOIN查询（应该生成相同的key）
        String sql14 = "SELECT u.name, o.order_no FROM users u JOIN orders o ON u.id = o.user_id WHERE u.age > 18";
        String sql15 = "SELECT u.name, o.order_no FROM users u JOIN orders o ON u.id = o.user_id WHERE u.age > 18";
        String key14 = generateCacheKey(sql14);
        String key15 = generateCacheKey(sql15);
        System.out.println("SQL14: " + sql14);
        System.out.println("Key14: " + key14);
        System.out.println("SQL15: " + sql15);
        System.out.println("Key15: " + key15);
        System.out.println("✅ Key14 == Key15: " + key14.equals(key15));
        System.out.println();

        // 测试用例9：带表别名的查询（应该生成相同的key，因为基础SQL结构相同）
        String sql16 = "SELECT * FROM users u WHERE u.age > 18";
        String sql17 = "SELECT * FROM users WHERE age > 18";
        String key16 = generateCacheKey(sql16);
        String key17 = generateCacheKey(sql17);
        System.out.println("SQL16: " + sql16);
        System.out.println("Key16: " + key16);
        System.out.println("SQL17: " + sql17);
        System.out.println("Key17: " + key17);
        System.out.println("✅ Key16 == Key17: " + key16.equals(key17));
        System.out.println();

        // 测试用例10：空WHERE条件的查询
        String sql18 = "SELECT * FROM users";
        String key18 = generateCacheKey(sql18);
        System.out.println("SQL18: " + sql18);
        System.out.println("Key18: " + key18);
        System.out.println();

        // 测试用例11：复杂混合条件（应该生成相同的key）
        String sql19 = "SELECT * FROM users WHERE (age > 18 OR vip = true) AND (city = '北京' OR city = '上海')";
        String sql20 = "SELECT * FROM users WHERE (city = '上海' OR city = '北京') AND (vip = true OR age > 18)";
        String key19 = generateCacheKey(sql19);
        String key20 = generateCacheKey(sql20);
        System.out.println("SQL19: " + sql19);
        System.out.println("Key19: " + key19);
        System.out.println("SQL20: " + sql20);
        System.out.println("Key20: " + key20);
        System.out.println("✅ Key19 == Key20: " + key19.equals(key20));
        System.out.println();

        // 测试用例12：IN条件（值顺序不同，应该生成相同的key）
        String sql21 = "SELECT * FROM users WHERE age IN (18, 25, 30)";
        String sql22 = "SELECT * FROM users WHERE age IN (25, 18, 30)";
        String key21 = generateCacheKey(sql21);
        String key22 = generateCacheKey(sql22);
        System.out.println("SQL21: " + sql21);
        System.out.println("Key21: " + key21);
        System.out.println("SQL22: " + sql22);
        System.out.println("Key22: " + key22);
        System.out.println("✅ Key21 == Key22: " + key21.equals(key22));
        System.out.println();

        // 测试用例13：LIKE条件（条件顺序不同，应该生成相同的key）
        String sql23 = "SELECT * FROM users WHERE name LIKE '张%' AND age > 18";
        String sql24 = "SELECT * FROM users WHERE age > 18 AND name LIKE '张%'";
        String key23 = generateCacheKey(sql23);
        String key24 = generateCacheKey(sql24);
        System.out.println("SQL23: " + sql23);
        System.out.println("Key23: " + key23);
        System.out.println("SQL24: " + sql24);
        System.out.println("Key24: " + key24);
        System.out.println("✅ Key23 == Key24: " + key23.equals(key24));
        System.out.println();

        // 测试用例14：BETWEEN条件（完全相同的查询，应该生成相同的key）
        String sql25 = "SELECT * FROM users WHERE age BETWEEN 18 AND 30";
        String sql26 = "SELECT * FROM users WHERE age BETWEEN 18 AND 30";
        String key25 = generateCacheKey(sql25);
        String key26 = generateCacheKey(sql26);
        System.out.println("SQL25: " + sql25);
        System.out.println("Key25: " + key25);
        System.out.println("SQL26: " + sql26);
        System.out.println("Key26: " + key26);
        System.out.println("✅ Key25 == Key26: " + key25.equals(key26));
        System.out.println();

        // 测试用例15：空字符串和null
        String key27 = generateCacheKey("");
        String key28 = generateCacheKey(null);
        System.out.println("Empty SQL Key: " + key27);
        System.out.println("Null SQL Key: " + key28);
        System.out.println();

        // 测试用例16：字段数量不同的查询（应该生成不同的key）
        String sql29 = "SELECT * FROM users WHERE age > 18";
        String sql30 = "SELECT * FROM users WHERE age > 18 AND status = 'active'";
        String key29 = generateCacheKey(sql29);
        String key30 = generateCacheKey(sql30);
        System.out.println("SQL29: " + sql29);
        System.out.println("Key29: " + key29);
        System.out.println("SQL30: " + sql30);
        System.out.println("Key30: " + key30);
        System.out.println("❌ Key29 == Key30: " + key29.equals(key30) + " (应该不同)");
        System.out.println();

        // 测试用例17：操作符不同的查询（应该生成不同的key）
        String sql31 = "SELECT * FROM users WHERE age > 18";
        String sql32 = "SELECT * FROM users WHERE age >= 18";
        String key31 = generateCacheKey(sql31);
        String key32 = generateCacheKey(sql32);
        System.out.println("SQL31: " + sql31);
        System.out.println("Key31: " + key31);
        System.out.println("SQL32: " + sql32);
        System.out.println("Key32: " + key32);
        System.out.println("❌ Key31 == Key32: " + key31.equals(key32) + " (应该不同)");
        System.out.println();

        // 测试用例18：三表JOIN查询（应该生成相同的key）
        String sql33 = "SELECT * FROM users u JOIN orders o ON u.id = o.user_id JOIN products p ON o.product_id = p.id WHERE u.age > 18";
        String sql34 = "SELECT * FROM users u JOIN orders o ON u.id = o.user_id JOIN products p ON o.product_id = p.id WHERE u.age > 18";
        String key33 = generateCacheKey(sql33);
        String key34 = generateCacheKey(sql34);
        System.out.println("SQL33: " + sql33);
        System.out.println("Key33: " + key33);
        System.out.println("SQL34: " + sql34);
        System.out.println("Key34: " + key34);
        System.out.println("✅ Key33 == Key34: " + key33.equals(key34));
        System.out.println();

        // 测试用例19：不同JOIN结构的查询（应该生成不同的key）
        String sql35 = "SELECT * FROM users u JOIN orders o ON u.id = o.user_id WHERE u.age > 18";
        String sql36 = "SELECT * FROM users u JOIN products p ON u.id = p.user_id WHERE u.age > 18";
        String key35 = generateCacheKey(sql35);
        String key36 = generateCacheKey(sql36);
        System.out.println("SQL35: " + sql35);
        System.out.println("Key35: " + key35);
        System.out.println("SQL36: " + sql36);
        System.out.println("Key36: " + key36);
        System.out.println("❌ Key35 == Key36: " + key35.equals(key36) + " (应该不同，因为JOIN结构不同)");
        System.out.println();

        // 测试用例20：JOIN条件不同的查询（应该生成不同的key）
        String sql37 = "SELECT * FROM users u JOIN orders o ON u.id = o.user_id WHERE u.age > 18";
        String sql38 = "SELECT * FROM users u JOIN orders o ON u.id = o.order_id WHERE u.age > 18";
        String key37 = generateCacheKey(sql37);
        String key38 = generateCacheKey(sql38);
        System.out.println("SQL37: " + sql37);
        System.out.println("Key37: " + key37);
        System.out.println("SQL38: " + sql38);
        System.out.println("Key38: " + key38);
        System.out.println("❌ Key37 == Key38: " + key37.equals(key38) + " (应该不同，因为JOIN条件不同)");
        System.out.println();

        // 测试用例21：ORDER BY子句不同的查询（应该生成不同的key）
        String sql39 = "SELECT * FROM users WHERE age > 18 ORDER BY name ASC";
        String sql40 = "SELECT * FROM users WHERE age > 18 ORDER BY name DESC";
        String key39 = generateCacheKey(sql39);
        String key40 = generateCacheKey(sql40);
        System.out.println("SQL39: " + sql39);
        System.out.println("Key39: " + key39);
        System.out.println("SQL40: " + sql40);
        System.out.println("Key40: " + key40);
        System.out.println("❌ Key39 == Key40: " + key39.equals(key40) + " (应该不同，因为ORDER BY不同)");
        System.out.println();

        // 测试用例22：GROUP BY子句不同的查询（应该生成不同的key）
        String sql41 = "SELECT * FROM users WHERE age > 18 GROUP BY age";
        String sql42 = "SELECT * FROM users WHERE age > 18 GROUP BY status";
        String key41 = generateCacheKey(sql41);
        String key42 = generateCacheKey(sql42);
        System.out.println("SQL41: " + sql41);
        System.out.println("Key41: " + key41);
        System.out.println("SQL42: " + sql42);
        System.out.println("Key42: " + key42);
        System.out.println("❌ Key41 == Key42: " + key41.equals(key42) + " (应该不同，因为GROUP BY不同)");
        System.out.println();

        // 测试用例23：LIMIT子句不同的查询（应该生成不同的key）
        String sql43 = "SELECT * FROM users WHERE age > 18 LIMIT 10";
        String sql44 = "SELECT * FROM users WHERE age > 18 LIMIT 20";
        String key43 = generateCacheKey(sql43);
        String key44 = generateCacheKey(sql44);
        System.out.println("SQL43: " + sql43);
        System.out.println("Key43: " + key43);
        System.out.println("SQL44: " + sql44);
        System.out.println("Key44: " + key44);
        System.out.println("❌ Key43 == Key44: " + key43.equals(key44) + " (应该不同，因为LIMIT不同)");
        System.out.println();

        // 测试用例24：复杂子句组合的查询（应该生成不同的key）
        String sql45 = "SELECT * FROM users WHERE age > 18 GROUP BY age ORDER BY age ASC LIMIT 10";
        String sql46 = "SELECT * FROM users WHERE age > 18 GROUP BY age ORDER BY age DESC LIMIT 10";
        String key45 = generateCacheKey(sql45);
        String key46 = generateCacheKey(sql46);
        System.out.println("SQL45: " + sql45);
        System.out.println("Key45: " + key45);
        System.out.println("SQL46: " + sql46);
        System.out.println("Key46: " + key46);
        System.out.println("❌ Key45 == Key46: " + key45.equals(key46) + " (应该不同，因为ORDER BY不同)");
        System.out.println();

        // 测试用例25：相同子句的查询（应该生成相同的key）
        String sql47 = "SELECT * FROM users WHERE age > 18 ORDER BY name ASC LIMIT 10";
        String sql48 = "SELECT * FROM users WHERE age > 18 ORDER BY name ASC LIMIT 10";
        String key47 = generateCacheKey(sql47);
        String key48 = generateCacheKey(sql48);
        System.out.println("SQL47: " + sql47);
        System.out.println("Key47: " + key47);
        System.out.println("SQL48: " + sql48);
        System.out.println("Key48: " + key48);
        System.out.println("✅ Key47 == Key48: " + key47.equals(key48) + " (应该相同，因为所有子句都相同)");
        System.out.println();

        // 测试用例26：DB2语法 - FETCH FIRST ... ROWS ONLY
        String sql49 = "SELECT * FROM users WHERE age > 18 ORDER BY name ASC FETCH FIRST 10 ROWS ONLY";
        String sql50 = "SELECT * FROM users WHERE age > 18 ORDER BY name ASC FETCH FIRST 20 ROWS ONLY";
        String key49 = generateCacheKey(sql49);
        String key50 = generateCacheKey(sql50);
        System.out.println("SQL49 (DB2): " + sql49);
        System.out.println("Key49: " + key49);
        System.out.println("SQL50 (DB2): " + sql50);
        System.out.println("Key50: " + key50);
        System.out.println("❌ Key49 == Key50: " + key49.equals(key50) + " (应该不同，因为FETCH FIRST不同)");
        System.out.println();

        // 测试用例27：DB2语法 - FETCH FIRST ... ROW ONLY (单数形式)
        String sql51 = "SELECT * FROM users WHERE age > 18 ORDER BY name ASC FETCH FIRST 1 ROW ONLY";
        String sql52 = "SELECT * FROM users WHERE age > 18 ORDER BY name ASC FETCH FIRST 1 ROW ONLY";
        String key51 = generateCacheKey(sql51);
        String key52 = generateCacheKey(sql52);
        System.out.println("SQL51 (DB2): " + sql51);
        System.out.println("Key51: " + key51);
        System.out.println("SQL52 (DB2): " + sql52);
        System.out.println("Key52: " + key52);
        System.out.println("✅ Key51 == Key52: " + key51.equals(key52) + " (应该相同，因为所有子句都相同)");
        System.out.println();

        // 测试用例28：混合语法 - MySQL LIMIT vs DB2 FETCH FIRST
        String sql53 = "SELECT * FROM users WHERE age > 18 ORDER BY name ASC LIMIT 10";
        String sql54 = "SELECT * FROM users WHERE age > 18 ORDER BY name ASC FETCH FIRST 10 ROWS ONLY";
        String key53 = generateCacheKey(sql53);
        String key54 = generateCacheKey(sql54);
        System.out.println("SQL53 (MySQL): " + sql53);
        System.out.println("Key53: " + key53);
        System.out.println("SQL54 (DB2): " + sql54);
        System.out.println("Key54: " + key54);
        System.out.println("❌ Key53 == Key54: " + key53.equals(key54) + " (应该不同，因为分页语法不同)");
        System.out.println();

        System.out.println("=== 测试完成 ===");
        System.out.println("\n总结：");
        System.out.println("✅ 相同逻辑的查询（条件顺序不同）应该生成相同的key");
        System.out.println("✅ 相同表结构的JOIN查询应该生成相同的key");
        System.out.println("❌ 不同值、不同字段、不同表名、不同操作符应该生成不同的key");
        System.out.println("❌ 不同JOIN结构、不同JOIN条件应该生成不同的key");
        System.out.println("❌ 不同ORDER BY、GROUP BY、LIMIT子句应该生成不同的key");
        System.out.println("✅ 相同ORDER BY、GROUP BY、LIMIT子句应该生成相同的key");
        System.out.println("✅ 支持MySQL/PostgreSQL的LIMIT语法");
        System.out.println("✅ 支持DB2的FETCH FIRST ... ROWS ONLY语法");
    }

    /**
     * 处理括号，使用token替换来保护括号内容
     */
    private static String processParenthesesWithTokens(String clause, Map<String, String> tokenMap) {
        if (clause == null || !clause.contains("(")) {
            return clause;
        }

        StringBuilder result = new StringBuilder();
        int i = 0;
        int tokenCounter = 0;

        while (i < clause.length()) {
            char c = clause.charAt(i);
            if (c == '(') {
                // 找到匹配的右括号
                int depth = 1;
                int j = i + 1;
                while (j < clause.length() && depth > 0) {
                    if (clause.charAt(j) == '(') depth++;
                    if (clause.charAt(j) == ')') depth--;
                    j++;
                }

                if (depth == 0) {
                    // 提取括号内的内容
                    String innerContent = clause.substring(i + 1, j - 1);

                    // 检查是否为简单条件（不需要括号）
                    if (isSimpleCondition(innerContent)) {
                        // 直接添加内容，不创建token
                        result.append(innerContent);
                    } else {
                        // 创建token并存储原始内容
                        String token = "TOKEN_" + (++tokenCounter);
                        tokenMap.put(token, "(" + innerContent + ")");
                        result.append(token);
                    }
                    i = j;
                } else {
                    result.append(c);
                    i++;
                }
            } else {
                result.append(c);
                i++;
            }
        }

        return result.toString();
    }

    /**
     * 判断是否为简单条件（不需要括号）
     */
    private static boolean isSimpleCondition(String condition) {
        if (condition == null || condition.trim().isEmpty()) {
            return true;
        }

        // 如果条件不包含AND或OR，则为简单条件
        return !condition.toLowerCase().contains(" and ") &&
               !condition.toLowerCase().contains(" or ");
    }

    /**
     * 按操作符分割SQL条件（用于没有空格的情况）
     */
    private static String[] splitByOperator(String condition) {
        // 常见的比较操作符
        String[] operators = {">=", "<=", "!=", "=", ">", "<", "like", "in", "between"};

        for (String op : operators) {
            if (condition.toLowerCase().contains(op)) {
                int index = condition.toLowerCase().indexOf(op);
                String fieldName = condition.substring(0, index).trim();
                String operator = condition.substring(index, index + op.length());
                String value = condition.substring(index + op.length()).trim();

                return new String[]{fieldName, operator, value};
            }
        }

        // 如果无法识别操作符，返回原始条件
        return new String[]{condition, "", ""};
    }

    /**
     * 按操作符分割，但保护括号内的内容
     */
    private static List<String> splitByOperator(String clause, String operatorPattern) {
        List<String> result = new ArrayList<>();
        if (clause == null || clause.trim().isEmpty()) {
            return result;
        }

        String[] parts = clause.split("(?i)" + operatorPattern);
        for (String part : parts) {
            String trimmed = part.trim();
            if (StrUtil.isNotBlank(trimmed)) {
                result.add(trimmed);
            }
        }

        return result;
    }

    /**
     * 标准化IN条件，对值进行排序
     */
    private static String normalizeInCondition(String value) {
        if (value == null || !value.contains(",")) {
            return value;
        }

        try {
            // 提取括号内的值
            String values = value.replaceAll("[()]", "").trim();
            String[] valueArray = values.split(",");

            // 清理每个值并排序
            List<String> cleanValues = new ArrayList<>();
            for (String val : valueArray) {
                String cleanVal = val.trim();
                if (!cleanVal.isEmpty()) {
                    cleanValues.add(cleanVal);
                }
            }

            Collections.sort(cleanValues);
            return "(" + String.join(",", cleanValues) + ")";

        } catch (Exception e) {
            return value;
        }
    }
}
