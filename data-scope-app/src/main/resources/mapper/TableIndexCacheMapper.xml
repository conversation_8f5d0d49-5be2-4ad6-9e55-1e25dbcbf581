<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datascope.app.mapper.TableIndexCacheMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.datascope.app.entity.TableIndexCache">
        <id column="id" property="id"/>
        <result column="table_id" property="tableId"/>
        <result column="datasource_id" property="datasourceId"/>
        <result column="index_name" property="indexName"/>
        <result column="index_type" property="indexType"/>
        <result column="is_unique" property="isUnique"/>
        <result column="is_primary" property="isPrimary"/>
        <result column="column_count" property="columnCount"/>
        <result column="description" property="description"/>
        <result column="raw_index_type" property="rawIndexType"/>
        <result column="database_type" property="databaseType"/>
        <result column="last_sync_time" property="lastSyncTime"/>
        <result column="sync_status" property="syncStatus"/>
        <result column="error_message" property="errorMessage"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, table_id, datasource_id, index_name, index_type, is_unique, is_primary,
        column_count, description, raw_index_type, database_type, last_sync_time,
        sync_status, error_message, created_at, updated_at
    </sql>

    <!-- 根据表ID查询索引缓存 -->
    <select id="selectByTableId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM table_index_cache
        WHERE table_id = #{tableId}
        ORDER BY index_name
    </select>

    <!-- 删除表的索引缓存 -->
    <delete id="deleteByTableId">
        DELETE FROM table_index_cache WHERE table_id = #{tableId}
    </delete>

    <!-- 批量插入索引缓存 -->
    <insert id="batchInsert">
        INSERT INTO table_index_cache (
            id, table_id, datasource_id, index_name, index_type, is_unique, is_primary,
            column_count, description, raw_index_type, database_type, last_sync_time,
            sync_status, error_message, created_at, updated_at
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.tableId}, #{item.datasourceId}, #{item.indexName},
                #{item.indexType}, #{item.isUnique}, #{item.isPrimary}, #{item.columnCount},
                #{item.description}, #{item.rawIndexType}, #{item.databaseType},
                #{item.lastSyncTime}, #{item.syncStatus}, #{item.errorMessage},
                NOW(), NOW()
            )
        </foreach>
    </insert>

</mapper>
