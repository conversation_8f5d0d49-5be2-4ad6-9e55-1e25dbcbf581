<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.datascope.app.mapper.TableIndexColumnCacheMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.datascope.app.entity.TableIndexColumnCache">
        <id column="id" property="id"/>
        <result column="index_cache_id" property="indexCacheId"/>
        <result column="column_name" property="columnName"/>
        <result column="position" property="position"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="is_primary_column" property="isPrimaryColumn"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, index_cache_id, column_name, position, sort_order, is_primary_column, 
        created_at, updated_at
    </sql>

    <!-- 根据索引缓存ID查询列信息 -->
    <select id="selectByIndexCacheId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM table_index_column_cache 
        WHERE index_cache_id = #{indexCacheId} 
        ORDER BY position
    </select>

    <!-- 删除索引的列缓存 -->
    <delete id="deleteByIndexCacheId">
        DELETE FROM table_index_column_cache WHERE index_cache_id = #{indexCacheId}
    </delete>

    <!-- 批量插入列缓存 -->
    <insert id="batchInsert">
        INSERT INTO table_index_column_cache (
            id, index_cache_id, column_name, position, sort_order, is_primary_column,
            created_at, updated_at
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.indexCacheId}, #{item.columnName}, #{item.position},
                #{item.sortOrder}, #{item.isPrimaryColumn}, NOW(), NOW()
            )
        </foreach>
    </insert>

    <!-- 批量删除索引的列缓存 -->
    <delete id="batchDeleteByIndexCacheIds">
        DELETE FROM table_index_column_cache 
        WHERE index_cache_id IN
        <foreach collection="indexCacheIds" item="indexCacheId" open="(" separator="," close=")">
            #{indexCacheId}
        </foreach>
    </delete>

</mapper>
