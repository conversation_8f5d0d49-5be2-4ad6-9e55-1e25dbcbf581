package com.datascope.app;

import com.datascope.app.config.QueryRateLimitAspect;
import com.datascope.app.config.RateLimitConfig;
import com.datascope.app.config.anno.QueryRateLimit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 限流功能测试类
 *
 * <AUTHOR>
 */
public class RateLimitTest {

    private QueryRateLimitAspect rateLimitAspect;
    private RateLimitConfig rateLimitConfig;

    @BeforeEach
    void setUp() {
        // 创建配置
        rateLimitConfig = new RateLimitConfig();
        RateLimitConfig.TokenBucket tokenBucket = new RateLimitConfig.TokenBucket();
        tokenBucket.setCapacity(5);  // 容量5
        tokenBucket.setRate(2);      // 每秒2个令牌
        rateLimitConfig.setTokenBucket(tokenBucket);

        // 创建限流切面（模拟）
        rateLimitAspect = new QueryRateLimitAspect();
        // 这里需要注入配置，但为了测试我们直接设置
    }

    /**
     * 测试令牌桶限流逻辑
     */
    @Test
    void testTokenBucketRateLimit() {
        System.out.println("🚀 开始测试令牌桶限流逻辑...");
        System.out.println("配置: 容量=" + rateLimitConfig.getTokenBucket().getCapacity() + 
                          ", 速率=" + rateLimitConfig.getTokenBucket().getRate() + "/秒");
        System.out.println("==================================");

        // 模拟快速发送请求
        int totalRequests = 10;
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger rejectCount = new AtomicInteger(0);

        System.out.println("发送 " + totalRequests + " 个请求，预期前5个成功，后5个被限流...");
        System.out.println("");

        for (int i = 1; i <= totalRequests; i++) {
            // 模拟令牌桶检查
            boolean allowed = checkTokenBucket(i);
            
            if (allowed) {
                successCount.incrementAndGet();
                System.out.println("请求 #" + i + ": ✅ 通过");
            } else {
                rejectCount.incrementAndGet();
                System.out.println("请求 #" + i + ": ❌ 被限流");
            }
            
            // 模拟请求间隔
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        System.out.println("");
        System.out.println("📊 测试结果:");
        System.out.println("成功请求: " + successCount.get());
        System.out.println("被限流请求: " + rejectCount.get());
        System.out.println("总请求: " + totalRequests);
        
        // 验证结果
        assert successCount.get() <= rateLimitConfig.getTokenBucket().getCapacity() : 
            "成功请求数不应超过令牌桶容量";
        assert rejectCount.get() > 0 : "应该有请求被限流";
        
        System.out.println("✅ 限流测试完成！");
    }

    /**
     * 模拟令牌桶检查逻辑
     */
    private boolean checkTokenBucket(int requestId) {
        // 简化的令牌桶逻辑
        int capacity = rateLimitConfig.getTokenBucket().getCapacity();
        
        // 模拟当前可用令牌数（静态变量来模拟状态）
        if (availableTokens > 0) {
            // 消耗一个令牌
            availableTokens--;
            return true;
        } else {
            return false;
        }
    }
    
    // 模拟令牌桶状态
    private static int availableTokens = 5;

    /**
     * 测试并发限流
     */
    @Test
    void testConcurrentRateLimit() throws InterruptedException {
        System.out.println("🚀 开始测试并发限流...");
        System.out.println("==================================");

        int threadCount = 10;
        int requestsPerThread = 2;
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger totalSuccess = new AtomicInteger(0);
        AtomicInteger totalReject = new AtomicInteger(0);

        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < requestsPerThread; j++) {
                        boolean allowed = checkTokenBucket(threadId * requestsPerThread + j);
                        if (allowed) {
                            totalSuccess.incrementAndGet();
                        } else {
                            totalReject.incrementAndGet();
                        }
                        Thread.sleep(50);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        System.out.println("📊 并发测试结果:");
        System.out.println("总成功请求: " + totalSuccess.get());
        System.out.println("总被限流请求: " + totalReject.get());
        System.out.println("总请求: " + (threadCount * requestsPerThread));
        
        System.out.println("✅ 并发限流测试完成！");
    }

    /**
     * 测试限流配置
     */
    @Test
    void testRateLimitConfig() {
        System.out.println("🚀 测试限流配置...");
        System.out.println("==================================");

        System.out.println("当前配置:");
        System.out.println("- 降级策略: " + rateLimitConfig.getFallbackStrategy());
        System.out.println("- Redis连接超时: " + rateLimitConfig.getRedis().getConnectionTimeout() + "ms");
        System.out.println("- Redis操作超时: " + rateLimitConfig.getRedis().getOperationTimeout() + "ms");
        System.out.println("- Redis键过期时间: " + rateLimitConfig.getRedis().getKeyExpireSeconds() + "s");
        System.out.println("- 令牌桶容量: " + rateLimitConfig.getTokenBucket().getCapacity());
        System.out.println("- 令牌桶速率: " + rateLimitConfig.getTokenBucket().getRate() + "/秒");
        System.out.println("- 突发容忍度: " + rateLimitConfig.getTokenBucket().getBurstTolerance());

        // 验证配置
        assert rateLimitConfig.getTokenBucket().getCapacity() > 0 : "令牌桶容量必须大于0";
        assert rateLimitConfig.getTokenBucket().getRate() > 0 : "令牌桶速率必须大于0";
        assert rateLimitConfig.getRedis().getConnectionTimeout() > 0 : "Redis连接超时必须大于0";

        System.out.println("✅ 限流配置测试完成！");
    }
}
