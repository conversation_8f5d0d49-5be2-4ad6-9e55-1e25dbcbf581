package com.datascope.facade;

import com.datascope.facade.dto.ParamSourceDTO;
import com.datascope.facade.dto.QueryParamDTO;
import com.datascope.facade.response.ResultResponse;

import java.util.Map;

/**
 * <AUTHOR>
 *
 * 集成查询dubbo服务
 */
public interface IntegrationQueryFacade {

    /**
     * 获取查询信息
     *
     * @param paramDTO 查询参数
     * @return 结果
     */
    ResultResponse<Map<String, Object>> query(QueryParamDTO paramDTO);

    /**
     * 获取参数信息
     *
     * @param id 参数id
     * @return 结果
     */
    ResultResponse<ParamSourceDTO> paramGet(String id);
}
