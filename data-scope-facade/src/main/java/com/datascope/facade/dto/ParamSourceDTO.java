package com.datascope.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 *
 * 参数信息
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ParamSourceDTO implements Serializable {

    private List<RequestParamDTO> requestParams;

    private List<ResponseParamDTO> responseParams;
}
