package com.datascope.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 * 集成查询参数
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class QueryParamDTO implements Serializable {

    /**
     * 集成ID
     */
    private String id;

    /**
     * 引用类型
     */
    private Map<String, Object> params;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 页大小
     */
    private Integer size;

    public Integer getPage() {
        if (page == null || page < 1) {
            return 1;
        }
        return page;
    }

    public Integer getSize() {
        if (size == null || size < 1) {
            return 20;
        }
        return size;
    }

    public Map<String, Object> getParams() {
        if (params == null) {
            return new HashMap<>();
        }
        return params;
    }
}
