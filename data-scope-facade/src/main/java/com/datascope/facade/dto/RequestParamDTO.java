package com.datascope.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 *
 * 参数信息
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class RequestParamDTO implements Serializable {

    /**
     * 字段key
     */
    private String name;

    /**
     * 字段v驼峰
     */
    private String underName;

    /**
     * 字段格式类型,比图text代表用户传字符串
     *  text: 输入框
     *  select: 枚举
     *  date: 日期
     *  date-range: 日期范围
     *  number_range: 金额区间
     */
    private String formType;

    /**
     * 字段中文描述
     */
    private String description;

    /**
     * 字段是否必填
     */
    private Boolean required;

    /**
     * 如果formType为select,则需要传枚举映射
     */
    private List<EnumDTO> enums;
}
