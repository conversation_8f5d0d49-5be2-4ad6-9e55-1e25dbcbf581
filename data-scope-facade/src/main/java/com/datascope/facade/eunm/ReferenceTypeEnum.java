package com.datascope.facade.eunm;

import lombok.Getter;

/**
 * <AUTHOR>
 *
 * 集成引用类型枚举
 */
@Getter
public enum ReferenceTypeEnum {

    /** YOP **/
    YOP("YOP")
    ;

    private final String value;

    ReferenceTypeEnum(String value) {
        this.value = value;
    }

    public static ReferenceTypeEnum getByValue(String value) {
        for (ReferenceTypeEnum type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return null;
    }
}
