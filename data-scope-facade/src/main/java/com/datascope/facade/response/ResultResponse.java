package com.datascope.facade.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ResultResponse<T> implements Serializable {

    public static final String SUCCESS_CODE = "0";

    public static final String CHECK_CODE = "-1";

    /**
     * 成功标识
     */
    private boolean success;

    /**
     * 返回码
     */
    private String code;

    /**
     * 返回信息
     */
    private String message;

    /**
     * 返回数据
     */
    private List<T> data;

    public static <T> ResultResponse<T> success(List<T> data) {
        return new ResultResponse<>(true, SUCCESS_CODE, null, data);
    }

    public static <T> ResultResponse<T> checkError(String message) {
        return new ResultResponse<>(false, CHECK_CODE, message, null);
    }
}
