package com.datascope.main;

import com.datascope.main.config.AppProperties;
import com.yeepay.g3.utils.common.InitializeUtils;
import com.yeepay.g3.utils.gmcrypt.utils.SMUtils;
import com.yeepay.springframework.boot.annotation.EnableSoa;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 数据范围应用程序入口
 */
@EnableAsync
@EnableScheduling
@SpringBootApplication(exclude = {
    FlywayAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class,
    SecurityAutoConfiguration.class,
    ManagementWebSecurityAutoConfiguration.class,
    DataSourceAutoConfiguration.class,
    RedisAutoConfiguration.class
})
@EnableTransactionManagement
@EnableConfigurationProperties({AppProperties.class})
@PropertySource(value = {"classpath:runtimecfg/base-yml.properties", "classpath:dbconf/DATA_SCOPE.properties"})
@ComponentScan(basePackages = {"com.datascope.*"})
@ConfigurationPropertiesScan("com.datascope")
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableSoa
public class DataScopeApplication {

    public static void main(String[] args) {
        InitializeUtils.initComponents();
        SMUtils.init();
        SpringApplication application = new SpringApplication(DataScopeApplication.class);
        application.run(args);
    }

}
