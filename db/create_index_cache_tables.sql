-- 创建表索引信息缓存表
CREATE TABLE `table_index_cache` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `table_id` varchar(32) NOT NULL COMMENT '表ID',
  `datasource_id` varchar(32) NOT NULL COMMENT '数据源ID',
  `index_name` varchar(255) NOT NULL COMMENT '索引名称',
  `index_type` varchar(50) DEFAULT NULL COMMENT '索引类型',
  `is_unique` tinyint(1) DEFAULT '0' COMMENT '是否唯一索引',
  `is_primary` tinyint(1) DEFAULT '0' COMMENT '是否主键索引',
  `column_count` int(11) DEFAULT '0' COMMENT '索引列数量',
  `description` varchar(500) DEFAULT NULL COMMENT '索引描述',
  `raw_index_type` varchar(10) DEFAULT NULL COMMENT '原始索引类型值',
  `database_type` varchar(50) DEFAULT NULL COMMENT '数据库类型',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `sync_status` varchar(20) DEFAULT 'SUCCESS' COMMENT '同步状态',
  `error_message` text COMMENT '同步错误信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_table_id` (`table_id`),
  KEY `idx_datasource_id` (`datasource_id`),
  KEY `idx_last_sync_time` (`last_sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表索引信息缓存表';

-- 创建表索引列信息缓存表
CREATE TABLE `table_index_column_cache` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `index_cache_id` varchar(32) NOT NULL COMMENT '索引缓存ID',
  `column_name` varchar(255) NOT NULL COMMENT '列名',
  `position` int(11) NOT NULL COMMENT '列位置',
  `sort_order` varchar(10) DEFAULT 'ASC' COMMENT '排序方式',
  `is_primary_column` tinyint(1) DEFAULT '0' COMMENT '是否主列',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_index_cache_id` (`index_cache_id`),
  KEY `idx_position` (`position`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表索引列信息缓存表';
