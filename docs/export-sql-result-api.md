# SQL查询结果导出API

## 概述

新增的SQL查询结果导出功能允许用户直接导出通过`/api/queries/execute-sql`接口执行的SQL查询结果。

## API接口

### 导出SQL查询结果

**接口地址：** `POST /api/excel/export-sql-result`

**功能描述：** 根据execute-sql接口的返回结果动态导出Excel文件

**请求参数：**

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| dataSourceId | String | 是 | 数据源ID |
| sql | String | 是 | 要执行的SQL语句 |
| parameters | Map<String, Object> | 否 | SQL参数 |
| page | Integer | 否 | 分页页码，默认1 |
| size | Integer | 否 | 分页大小，默认10 |
| sort | String | 否 | 排序字段 |
| schemaId | String | 否 | 模式ID |
| fileName | String | 否 | 导出文件名（不含扩展名），默认"SQL查询结果"，会自动添加日期时间戳 |

**请求示例：**

```json
{
  "dataSourceId": "ds_123456",
  "sql": "SELECT id, name, age, email FROM users WHERE age > ?",
  "parameters": {
    "age": 18
  },
  "page": 1,
  "size": 100,
  "fileName": "用户数据导出"
}
```

**文件名示例：**
- 如果传入fileName为"用户数据导出"，最终文件名将是：`用户数据导出_20241201_143022.xlsx`
- 如果不传fileName，最终文件名将是：`SQL查询结果_20241201_143022.xlsx`

**响应：** 直接返回Excel文件下载

**使用说明：**

1. 该接口会先调用`/api/queries/execute-sql`接口执行SQL查询
2. 根据查询结果的字段信息动态生成Excel表头
3. 将查询结果数据导出为Excel文件
4. 支持分页查询，但导出时会获取所有数据
5. 文件名会自动添加日期时间戳后缀（格式：yyyyMMdd_HHmmss）

**错误处理：**

- 如果SQL执行失败，会返回错误信息
- 如果数据源不存在或无权限，会抛出相应异常
- 如果查询结果为空，会导出空的Excel文件

**注意事项：**

1. 只支持SELECT查询语句
2. 导出大量数据时请注意性能影响
3. 建议在导出前先通过`/api/queries/execute-sql`接口测试SQL语句
4. 文件名会自动添加日期时间戳（格式：yyyyMMdd_HHmmss），避免重复

## 使用场景

1. **数据分析报告导出**：执行复杂查询后直接导出结果
2. **数据备份**：定期导出重要数据
3. **报表生成**：基于SQL查询生成各种报表
4. **数据分享**：将查询结果以Excel格式分享给其他用户

## 技术实现

- 复用现有的`QueryService.executeSQL`方法执行查询
- 使用`ExcelExportService.exportFromQueryResult`方法进行导出
- 动态根据查询结果的字段信息生成Excel表头
- 支持各种数据类型的数据导出
