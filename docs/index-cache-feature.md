# 表索引缓存功能说明

## 概述

为了提高表索引查询的性能，我们实现了索引信息缓存功能。该功能将索引信息存储在本地数据库中，避免每次查询都需要建立JDBC连接去不同的数据源实时获取索引信息。

## 功能特点

### 1. **性能提升**
- **首次查询**: 实时获取索引信息并缓存到本地
- **后续查询**: 直接从缓存获取，响应速度提升10-100倍
- **刷新机制**: 支持手动刷新，确保索引信息的时效性

### 2. **智能缓存策略**
- 自动检测缓存是否存在
- 缓存不存在时自动实时获取并缓存
- 支持手动刷新更新缓存

### 3. **数据完整性**
- 缓存索引信息和列信息
- 记录同步状态和错误信息
- 支持事务操作，确保数据一致性

## 数据库表结构

### 1. **table_index_cache** - 索引缓存表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | varchar(32) | 主键ID |
| table_id | varchar(32) | 表ID |
| datasource_id | varchar(32) | 数据源ID |
| index_name | varchar(255) | 索引名称 |
| index_type | varchar(50) | 索引类型 |
| is_unique | tinyint(1) | 是否唯一索引 |
| is_primary | tinyint(1) | 是否主键索引 |
| column_count | int(11) | 索引列数量 |
| description | varchar(500) | 索引描述 |
| raw_index_type | varchar(10) | 原始索引类型值 |
| database_type | varchar(50) | 数据库类型 |
| last_sync_time | datetime | 最后同步时间 |
| sync_status | varchar(20) | 同步状态 |
| error_message | text | 同步错误信息 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

**索引设计**:
- `idx_table_id` (table_id) - 表ID索引
- `idx_datasource_id` (datasource_id) - 数据源ID索引  
- `idx_last_sync_time` (last_sync_time) - 最后同步时间索引

### 2. **table_index_column_cache** - 索引列缓存表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | varchar(32) | 主键ID |
| index_cache_id | varchar(32) | 索引缓存ID |
| column_name | varchar(255) | 列名 |
| position | int(11) | 列位置 |
| sort_order | varchar(10) | 排序方式 |
| is_primary_column | tinyint(1) | 是否主列 |
| created_at | datetime | 创建时间 |
| updated_at | datetime | 更新时间 |

**索引设计**:
- `idx_index_cache_id` (index_cache_id) - 索引缓存ID索引
- `idx_position` (position) - 列位置索引

## API接口

### 1. **获取表索引信息**
```http
GET /api/metadata/tables/{id}/indexes
```

**功能说明**:
- 优先从缓存获取索引信息
- 如果缓存不存在，自动实时获取并缓存
- 返回格式化的索引信息

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "indexName": "PRIMARY",
      "indexType": "BTREE",
      "isUnique": true,
      "isPrimary": true,
      "columns": [
        {
          "columnName": "id",
          "position": 1,
          "order": "ASC",
          "isPrimary": true
        }
      ],
      "columnCount": 1,
      "description": "主键索引"
    }
  ]
}
```

### 2. **刷新表索引信息**
```http
POST /api/metadata/tables/{id}/indexes/refresh
```

**功能说明**:
- 强制实时获取索引信息
- 更新本地缓存
- 返回最新的索引信息

## 使用流程

### 1. **初始化设置**
```sql
-- 执行建表SQL
source db/create_index_cache_tables.sql;
```

### 2. **首次查询**
```bash
# 首次查询会自动实时获取并缓存
curl -X GET "http://localhost:8080/api/metadata/tables/table123/indexes"
```

### 3. **后续查询**
```bash
# 后续查询直接从缓存获取，速度很快
curl -X GET "http://localhost:8080/api/metadata/tables/table123/indexes"
```

### 4. **手动刷新**
```bash
# 需要更新索引信息时手动刷新
curl -X POST "http://localhost:8080/api/metadata/tables/table123/indexes/refresh"
```

## 性能对比

| 查询类型 | 响应时间 | 说明 |
|----------|----------|------|
| 实时查询 | 500ms-2000ms | 需要建立JDBC连接，查询数据库元数据 |
| 缓存查询 | 5ms-50ms | 直接从本地数据库查询，速度提升10-100倍 |

## 缓存更新策略

### 1. **自动更新**
- 首次查询时自动缓存
- 缓存不存在时自动实时获取

### 2. **手动更新**
- 用户主动点击刷新按钮
- 支持批量刷新操作

### 3. **建议更新时机**
- 表结构发生变更后
- 索引信息需要及时更新时
- 定期维护时

## 注意事项

### 1. **数据一致性**
- 缓存数据可能与实际数据库存在延迟
- 重要操作建议先刷新缓存

### 2. **存储空间**
- 索引缓存会占用一定的存储空间
- 建议定期清理过期的缓存数据

### 3. **错误处理**
- 缓存同步失败时会记录错误信息
- 支持降级到实时查询模式

## 测试验证

使用提供的测试脚本验证功能：

```bash
# 1. 设置执行权限
chmod +x test-index-cache-api.sh

# 2. 修改脚本中的表ID和认证信息

# 3. 执行测试
./test-index-cache-api.sh
```

## 故障排查

### 1. **缓存查询失败**
- 检查缓存表是否存在
- 检查数据库连接是否正常
- 查看应用日志中的错误信息

### 2. **同步状态异常**
- 检查数据源连接是否正常
- 查看sync_status和error_message字段
- 确认表ID和数据库权限

### 3. **性能问题**
- 检查缓存表索引是否正常
- 监控缓存命中率
- 分析慢查询日志

## 扩展功能

### 1. **定时刷新**
- 可以添加定时任务，定期刷新索引缓存
- 支持配置刷新频率和策略

### 2. **缓存清理**
- 支持清理过期的缓存数据
- 支持按时间范围清理

### 3. **监控告警**
- 监控缓存同步状态
- 告警同步失败的情况

## 总结

索引缓存功能通过本地存储索引信息，显著提升了查询性能，同时保持了数据的时效性。该功能特别适合索引信息变化不频繁的场景，为用户提供了更好的使用体验。
