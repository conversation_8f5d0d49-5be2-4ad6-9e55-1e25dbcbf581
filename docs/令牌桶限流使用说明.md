# 令牌桶限流使用说明

## 📖 概述

本文档详细说明了数据查询系统中的令牌桶限流机制，包括配置、使用方法和故障排查。

## 🏗️ 架构设计

### 核心组件

- **QueryRateLimitAspect**: 限流切面，负责拦截和限流控制
- **Redis**: 存储令牌桶状态，支持分布式限流
- **Lua脚本**: 原子操作保证限流逻辑的一致性

### 限流策略

系统采用**令牌桶算法**实现限流：

- **容量 (Capacity)**: 令牌桶的最大容量
- **速率 (Rate)**: 每秒生成的令牌数量
- **请求令牌**: 每次请求消耗的令牌数量（默认为1）

## ⚙️ 配置说明

### 配置文件

在 `application.yml` 中配置令牌桶参数：

```yaml
datascope:
  rate-limit:
    token-bucket:
      capacity: 200      # 令牌桶容量
      rate: 10          # 每秒生成令牌数量
```

### 注解配置

在需要限流的接口上添加 `@QueryRateLimit` 注解：

```java
@QueryRateLimit(
    maxRequestsPerSecond = 100,  // 每秒最大请求数
    errorMessage = "请求过于频繁，请稍后重试"
)
public ResultResponse query(@RequestBody QueryParamDTO param) {
    // 业务逻辑
}
```

## 🔧 使用方法

### 1. 基本使用

```java
@RestController
@RequestMapping("/api/query")
public class QueryController {
    
    @PostMapping("/data")
    @QueryRateLimit(maxRequestsPerSecond = 50)
    public ResultResponse queryData(@RequestBody QueryParamDTO param) {
        // 业务逻辑
        return ResultResponse.success(data);
    }
}
```

### 2. 自定义错误消息

```java
@QueryRateLimit(
    maxRequestsPerSecond = 100,
    errorMessage = "系统繁忙，请稍后重试"
)
```

### 3. 不同接口不同限流

```java
// 查询接口：每秒100次
@QueryRateLimit(maxRequestsPerSecond = 100)
public ResultResponse query() { ... }

// 导出接口：每秒10次
@QueryRateLimit(maxRequestsPerSecond = 10)
public ResultResponse export() { ... }
```

## 🧠 令牌桶算法原理

### 算法流程

1. **初始化**: 首次请求时，令牌桶装满令牌
2. **令牌生成**: 根据时间差和速率生成新令牌
3. **令牌消耗**: 请求到达时消耗令牌
4. **限流判断**: 令牌不足时拒绝请求

### 数学公式

```
新令牌数 = min(当前令牌数 + (当前时间 - 上次时间) × 速率, 容量)
```

### 示例场景

**配置**: 容量=200, 速率=10/秒

- **T0时刻**: 200个令牌可用
- **T1时刻** (1秒后): 200 + 10 = 210 → 200个令牌（达到容量上限）
- **T2时刻** (2秒后): 200 + 20 = 220 → 200个令牌
- **持续服务**: 每秒最多处理10个请求

## 📊 监控和日志

### 日志输出

系统会输出详细的限流日志：

```
全局限流检查 - 配置容量: 200, 配置速率: 10, 注解限制: 100
令牌桶检查 - Key: global:rate:limit, 容量: 200, 速率: 10, 请求: 1
```

### 监控指标

- **限流触发次数**: 记录被拒绝的请求数量
- **令牌消耗情况**: 监控令牌桶的使用状态
- **响应时间**: 观察限流对性能的影响

## 🚨 故障排查

### 常见问题

#### 1. 每次请求都触发限流

**现象**: 所有请求都被拒绝，返回限流错误

**可能原因**:
- 令牌桶容量设置过小
- 令牌生成速率过低
- Lua脚本执行异常

**排查步骤**:
1. 检查配置文件中的容量和速率设置
2. 查看Redis中令牌桶的状态
3. 检查Lua脚本执行日志

**解决方案**:
```yaml
# 增加令牌桶容量和速率
datascope:
  rate-limit:
    token-bucket:
      capacity: 500    # 从200增加到500
      rate: 50         # 从10增加到50
```

#### 2. Redis连接异常

**现象**: 出现 `RedisSystemException` 错误

**可能原因**:
- Redis服务不可用
- 网络连接问题
- Redis配置错误

**排查步骤**:
1. 检查Redis服务状态
2. 验证Redis连接配置
3. 查看网络连通性

#### 3. Lua脚本执行失败

**现象**: 出现 `ERR Error compiling script` 错误

**可能原因**:
- Lua脚本语法错误
- Redis版本不兼容
- 脚本参数错误

**排查步骤**:
1. 检查Lua脚本语法
2. 验证Redis版本
3. 确认脚本参数传递正确

### 调试方法

#### 1. 启用调试日志

```yaml
logging:
  level:
    com.datascope.app.config.QueryRateLimitAspect: DEBUG
```

#### 2. 检查Redis数据

```bash
# 查看令牌桶状态
redis-cli HGETALL "global:rate:limit"

# 查看过期时间
redis-cli TTL "global:rate:limit"
```

#### 3. 手动测试Lua脚本

```bash
# 在Redis中直接执行脚本
redis-cli EVAL "$(cat token_bucket.lua)" 1 "test:key" 1234567890 200 10 1
```

## 🔄 性能优化

### 1. Redis优化

- **连接池配置**: 合理设置连接池大小
- **序列化优化**: 使用高效的序列化方式
- **批量操作**: 减少Redis往返次数

### 2. 限流参数调优

- **容量设置**: 根据系统承载能力设置
- **速率调整**: 根据业务峰值调整
- **过期时间**: 合理设置Redis键的过期时间

### 3. 监控告警

- **限流阈值告警**: 当限流触发频率过高时告警
- **性能监控**: 监控限流对系统性能的影响
- **容量预警**: 当令牌桶容量不足时预警

## 📝 最佳实践

### 1. 配置建议

- **容量设置**: 建议设置为预期QPS的2-3倍
- **速率设置**: 建议设置为预期QPS的1.5倍
- **过期时间**: 建议设置为24小时（86400秒）

### 2. 使用建议

- **合理限流**: 避免过度限流影响正常业务
- **分级限流**: 不同接口设置不同的限流策略
- **动态调整**: 根据业务情况动态调整限流参数

### 3. 监控建议

- **实时监控**: 实时监控限流触发情况
- **趋势分析**: 分析限流趋势，预测容量需求
- **告警设置**: 设置合理的告警阈值

## 🔗 相关链接

- [Redis官方文档](https://redis.io/documentation)
- [Spring Boot限流指南](https://spring.io/guides/gs/spring-boot/)
- [令牌桶算法详解](https://en.wikipedia.org/wiki/Token_bucket)

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 查看本文档的故障排查部分
2. 检查系统日志和监控指标
3. 联系技术支持团队

---

*最后更新时间: 2025-08-28*
*文档版本: v1.0*
